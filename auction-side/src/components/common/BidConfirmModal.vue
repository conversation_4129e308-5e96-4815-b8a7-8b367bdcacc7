<script setup>
  import termsJa from '@/assets/pdf/bid/利用規約_20250326.pdf'
  import termsEn from '@/assets/pdf/bid/terms_of_service_20250324.pdf'
  import ModalDialog from '../common/ModalDialog.vue'
  import {computed, ref, defineModel, watch} from 'vue'
  import {useBidConfirmStore} from '../../stores/bidConfirm'
  import {priceLocaleString} from '../../composables/common'
  import useApi from '../../composables/useApi'
  import {useMessageDialogStore} from '../../stores/messag-dialog'
  import {CLASSIFICATIONS, PATH_NAME} from '../../defined/const'
  import useSearchProducts from '../../composables/searchProducts'
  import useGetItemDetails from '../../composables/getItemDetails'
  import {useRoute} from 'vue-router'
  import {eventBus} from '../../utils'
  import {useCognitoAuthStore} from '@/stores/cognitoAuth'

  const emit = defineEmits(['refresh'])
  const props = defineProps(['isAscendingAuction'])

  const open = defineModel()

  const current = ref('ja')
  const route = useRoute()
  const {apiExecute, parseHtmlResponseError} = useApi()
  const bid = useBidConfirmStore()
  const messageDialog = useMessageDialogStore()
  const {search: searchList} = useSearchProducts()
  const {search: searchDetail} = useGetItemDetails()
  const hasBid = ref(false)
  // const agree = ref(false)

  const auth = useCognitoAuthStore()

  const termsSrc = computed(() => {
    return current.value === 'ja' ? termsJa : termsEn
  })
  const showBidResult = computed(() => bid.showBidResult)
  const exhibitionList = computed(() => bid.data || [])
  const allExhTotalPrice = computed(() => {
    return priceLocaleString(
      exhibitionList.value.reduce((acc, exh) => acc + (exh?.bidTotalPrice || 0), 0)
    )
  })

  const sendBid = async (
    inputParams = [{exhibitionItemNo: null, bidPrice: null, bidQuantity: null}]
  ) => {
    const params = {
      bidItems: inputParams.map(({exhibitionItemNo, bidPrice, bidQuantity}) => ({
        exhibitionItemNo,
        bidPrice,
        bidQuantity,
      })),
    }
    await apiExecute('private/bid-items', params)
      .then(response => {
        bid.data.map(exh => {
          exh.bidList.map(item => {
            item.errorMessage = null
            const tmp = response?.bidList?.find(
              x => String(item.exhibitionItemNo) === String(x.exhibition_item_no)
            )
            item.errorMessage = tmp?.errorMessage
          })
          exh.bidTotalPrice = exh.bidList.reduce((acc, item) => {
            if (item.errorMessage) {
              return acc
            }
            return acc + (item.bidTotalPrice || 0)
          }, 0)
        })
      })
      .catch(error => {
        const errMsg = parseHtmlResponseError(error)
        messageDialog.setShowMessage(errMsg, {isErr: true})
      })
  }

  const handleBid = async () => {
    if (!bid.agree) {
      return
    }
    if (auth.isAuthenticated) {
      const params = exhibitionList.value
        .map(exh => exh.bidList)
        .flat()
        .map(item => ({
          exhibitionItemNo: item.exhibitionItemNo,
          bidQuantity: item.bidQuantity,
          bidPrice: item.bidPrice,
        }))
      await sendBid(params)
      bid.setShowBidResult(true)
      hasBid.value = true
      eventBus.emit('onBidSuccess', params[0].exhibitionItemNo, params[0].bidPrice)
      // open.value = false
      // messageDialog.setShowMessage(t('productDetail.bidModal.bidSuccessFulMessage'), {
      //   name            : 'bid-success',
      //   showOkButton    : true,
      //   showCloseButton : false
      // })
    } else {
      open.value = false
      messageDialog.setShowMessage('ログインが必要です。', {
        showOkButton: true,
        showCloseButton: false,
      })
    }
  }

  const closeBidConfirmModal = () => {
    open.value = false
    if (hasBid.value) {
      const classification = props.isAscendingAuction
        ? CLASSIFICATIONS.ASCENDING
        : CLASSIFICATIONS.SEALED
      // Only emit 'refresh' if a bid was made and send isAscendingAuction as props to prevent switch to sealed tab 「ascending」 bid
      emit('refresh', classification)
      hasBid.value = false
    }
  }

  watch(
    () => messageDialog.clickedOk,
    async value => {
      if (value) {
        switch (messageDialog.dialogName) {
          case 'bid-success':
            if (route.path === PATH_NAME.FAVORITES) {
              searchList({favorite: true})
            } else if (route.path === PATH_NAME.BIDS) {
              searchList({bidding: true, unSoldOut: true})
            } else if (route.path === PATH_NAME.DETAIL) {
              await searchDetail(route.params.manageNo ?? '')
            }
            break
          default:
            break
        }
      }
      messageDialog.clickedOk = false
    }
  )
</script>
<template>
  <ModalDialog v-model="open" width="900px" @refresh-on-close="closeBidConfirmModal">
    <div class="item-wrap">
      <div class="item-panel">
        <figure>
          <div class="wrap_pct">
            <img src="@/assets/img/stock/01.jpg" alt="" />
          </div>
        </figure>
        <div class="product-wrap">
          <div class="modal-item-name">
            LOUIS VUITTON ハンドバッグ トートバッグ サック・プラ モノグラム M51140 レザー ブラウン
            ゴールド金具 中古
          </div>
          <div class="sell">
            <div class="price">
              <span class="label">入札価格</span
              ><span class="price-v">3,980,000<span class="unit">円</span></span>
            </div>
            <div class="sch-to-end">
              <div class="other-info">
                <div class="view">
                  <img src="@/assets/img/common/icn_eye_list.svg" /><span>100</span>
                </div>
                <div class="favorite">
                  <img src="@/assets/img/common/icn_favorite.svg" /><span>30</span>
                </div>
                <div class="bid"><img src="@/assets/img/common/icn_bid.svg" /><span>12</span></div>
              </div>
              <div class="end-date">
                <img src="@/assets/img/common/icn_clock_list.png" />
                <div class="end-l">終了予定</div>
                <div class="end-v"><span>8月31日</span><span>22:20</span></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <p class="note-bid">お間違いなければ入札ボタンをクリックしてください。</p>
    <div class="button-wrap">
      <button class="btn bid-cancel"><span>キャンセル</span></button>
      <button class="btn confirm"><span>入札確定する</span></button>
    </div>
  </ModalDialog>
</template>

<style scoped lang="scss">
  .btn {
    width: 100px;
    height: 50px;
    font-size: 16px;
    font-weight: bold;
    color: #fff;
    background-color: #007bff;
    border: none;
    border-radius: 4px;
    cursor: pointer;

    &:disabled {
      background-color: #6c757d;
      cursor: not-allowed;
    }
  }

  .button-bid {
    margin: 20px;
  }

  .terms-agreement {
    margin: 15px 20px;
    text-align: center;

    .checkbox-container {
      display: inline-flex;
      align-items: center;
      cursor: pointer;

      .checkbox-input {
        margin-right: 8px;
      }

      .checkbox-parts {
        font-size: 14px;

        a {
          color: #007bff;
          text-decoration: underline;

          &:hover {
            text-decoration: none;
          }
        }
      }
    }
  }
</style>
