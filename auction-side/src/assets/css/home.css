@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *商品情報
 * *********************************************************************** */
#main #heroes {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: 340px;
  background: url('../img/home/<USER>') center top/cover no-repeat;
}
@media screen and (max-width: 767px) {
  #main #heroes {
    width: 100%;
    height: 40vw;
    background: url('../img/home/<USER>') center no-repeat;
    background-size: auto 100%;
  }
}
#main #heroes .catch {
  color: #88a8bc;
  font-size: 1.6rem;
  font-weight: 400;
  font-family: 'Futura', 'Avenir', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}
@media screen and (max-width: 767px) {
  #main #heroes .catch {
    font-size: 4vw;
  }
}
#main #heroes .bnr {
  position: absolute;
  top: 5px;
  right: 20px;
  width: 200px;
}
@media screen and (max-width: 767px) {
  #main #heroes .bnr {
    top: calc(50% - 46px);
    right: 7px;
    width: 110px;
  }
}
#main #heroes .bnr a:hover {
  opacity: 1;
}
#main #heroes .bnr img {
  width: 100%;
  height: auto;
}
#main #heroes .bnr:hover {
  -webkit-transform: translateY(3px);
  transform: translateY(3px);
  -webkit-transition: all 0.5s 0s ease;
  transition: all 0.5s 0s ease;
  cursor: pointer;
}
#main .nav-list-mode-wrap {
  width: 100%;
  padding: 1.5rem 1rem;
  background-color: #333;
}
@media screen and (max-width: 767px) {
  #main .nav-list-mode-wrap {
    padding: 0;
  }
}
#main .nav-list-mode-wrap .container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 3rem;
  width: 900px;
  margin: 0 auto;
  padding: 0.5rem;
}
@media screen and (max-width: 767px) {
  #main .nav-list-mode-wrap .container {
    width: 100%;
    gap: 4vw;
    padding: 5vw 4vw;
  }
}
#main .nav-list-mode-wrap .container a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: auto;
  padding: 0;
  text-align: center;
  cursor: pointer;
  border: 1px solid #333;
}
@media screen and (max-width: 767px) {
  #main .nav-list-mode-wrap .container a {
    width: auto;
    padding: 0;
  }
}
#main .nav-list-mode-wrap .container a:hover {
  opacity: 1;
}
#main .nav-list-mode-wrap .container a:hover span {
  color: rgb(180, 204, 222);
}
#main .nav-list-mode-wrap .container a span {
  color: #fff;
  font-size: 1rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main .nav-list-mode-wrap .container a span {
    display: inline;
    width: auto;
    font-size: 3.2vw;
    font-weight: 600;
    text-align: center;
  }
}
#main #search {
  width: 100%;
  padding: 50px 2rem 30px;
  background-color: #ecf2f7;
}
@media screen and (max-width: 767px) {
  #main #search {
    padding: 7vw 4vw;
  }
}
#main #search .cont-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 1180px;
  max-width: 100%;
  margin: 0 auto;
}
#main #search .cont-wrap .search-keyword {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 920px;
  max-width: calc(100% - 52px);
  height: 50px;
  line-height: 50px;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  border-right: none;
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap .search-keyword {
    width: 100%;
    max-width: calc(100% - 12vw);
    height: 12vw;
  }
}
#main #search .cont-wrap .search-keyword input {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 400px;
  flex: 1 1 400px;
  height: 48px;
  padding: 0 0 0 20px;
  font-size: 1rem;
  font-weight: 500;
  line-height: 48px;
  background-color: transparent;
  border: none;
}
@media screen and (max-width: 1080px) {
  #main #search .cont-wrap .search-keyword input {
    height: 30px;
  }
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap .search-keyword input {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 100px;
    flex: 1 1 100px;
    max-width: 70vw;
    padding: 0 0 0 2vw;
    font-size: 3vw;
    line-height: 12vw;
  }
}
#main #search .cont-wrap .search-keyword input::-webkit-input-placeholder {
  color: #ccc;
  font-weight: 500;
}
#main #search .cont-wrap .search-keyword input::-moz-placeholder {
  color: #ccc;
  font-weight: 500;
}
#main #search .cont-wrap .search-keyword input:-ms-input-placeholder {
  color: #ccc;
  font-weight: 500;
}
#main #search .cont-wrap .search-keyword input::-ms-input-placeholder {
  color: #ccc;
  font-weight: 500;
}
#main #search .cont-wrap .search-keyword input::placeholder {
  color: #ccc;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap .search-keyword input::-webkit-input-placeholder {
    font-size: 3vw;
    -webkit-transform: translateY(-0.1vw);
    transform: translateY(-0.1vw);
  }
  #main #search .cont-wrap .search-keyword input::-moz-placeholder {
    font-size: 3vw;
    transform: translateY(-0.1vw);
  }
  #main #search .cont-wrap .search-keyword input:-ms-input-placeholder {
    font-size: 3vw;
    transform: translateY(-0.1vw);
  }
  #main #search .cont-wrap .search-keyword input::-ms-input-placeholder {
    font-size: 3vw;
    transform: translateY(-0.1vw);
  }
  #main #search .cont-wrap .search-keyword input::placeholder {
    font-size: 3vw;
    -webkit-transform: translateY(-0.1vw);
    transform: translateY(-0.1vw);
  }
}
#main #search .cont-wrap .search-keyword input.side-search-keyword {
  height: 48px;
  line-height: 40px;
  padding-top: 0;
  padding-bottom: 0;
}
@media screen and (max-width: 1080px) {
  #main #search .cont-wrap .search-keyword input.side-search-keyword {
    height: 30px;
    line-height: 30px;
  }
}
#main #search .cont-wrap .search-keyword .btn-category {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: static;
  width: auto;
  height: 36px;
  margin: 0 1rem 0 0;
  padding: 0;
  background-color: #e5eef5;
  border: 1px solid #d3dde4;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap .search-keyword .btn-category {
    margin: 0 1.3vw 0 0;
    height: 9vw;
  }
}
#main #search .cont-wrap .search-keyword .btn-category a {
  position: relative;
  padding: 0 30px 0 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap .search-keyword .btn-category a {
    padding: 1vw 2vw;
  }
}
#main #search .cont-wrap .search-keyword .btn-category a span {
  color: #333;
  font-size: 0.7rem;
  font-weight: 600;
  line-height: 1;
  -webkit-transform: translateY(0px);
  transform: translateY(0px);
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap .search-keyword .btn-category a span {
    font-size: 2.7vw;
    font-weight: 600;
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
  }
}
#main #search .cont-wrap .search-keyword .btn-category a:after {
  content: '';
  position: absolute;
  right: 13px;
  top: calc(50% - 1px);
  width: 4px;
  height: 4px;
  border-right: 2px solid #b9c7d0;
  border-bottom: 2px solid #b9c7d0;
  -webkit-transform: translateY(-50%) rotate(45deg);
  transform: translateY(-50%) rotate(45deg);
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap .search-keyword .btn-category a:after {
    content: none;
  }
}
#main #search .cont-wrap .search-keyword .filter-panel {
  position: absolute;
  top: 50px;
  right: 0;
  display: none;
  width: 700px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  z-index: 10;
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap .search-keyword .filter-panel {
    width: 100%;
    top: 12vw;
  }
}
#main #search .cont-wrap .search-keyword .filter-panel.is-active {
  display: block;
}
#main #search .cont-wrap .search-keyword .filter-panel .panel-body {
  padding: 8px;
  background-color: #fff;
  border: 1px solid rgb(180, 204, 222);
  -webkit-box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.15);
  box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.15);
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap .search-keyword .filter-panel .panel-body {
    padding: 4vw;
  }
}
#main #search .cont-wrap .search-keyword .filter-panel .panel-body .list-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  margin: 0.8rem 0 0;
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap .search-keyword .filter-panel .panel-body .list-wrap {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }
}
#main #search .cont-wrap .search-keyword .filter-panel .panel-body .list-wrap .list {
  width: 140px;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap .search-keyword .filter-panel .panel-body .list-wrap .list {
    width: 50%;
  }
}
#main #search .cont-wrap .search-keyword .filter-panel .panel-body .list-wrap .list li {
  margin: 0 12px 10px 12px;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.2;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap .search-keyword .filter-panel .panel-body .list-wrap .list li {
    margin: 0 1vw 2.5vw 2vw;
  }
}
#main #search .cont-wrap .search-keyword .filter-panel .panel-body .list-wrap .list li:hover {
  color: #427fae;
}
#main #search .cont-wrap .search-keyword .filter-panel .panel-body .list-wrap .list li.label {
  font-size: 0.75rem;
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap .search-keyword .filter-panel .panel-body .list-wrap .list li.label {
    font-size: 3.2vw;
  }
}
#main #search .cont-wrap .search-keyword .filter-panel .panel-body .list-wrap .list li.active {
  color: #427fae;
  font-weight: 600;
}
#main #search .cont-wrap .search-keyword .filter-panel .panel-body .close-filter {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}
#main #search .cont-wrap .search-keyword .filter-panel .panel-body .close-filter span {
  display: block;
  position: relative;
  width: 13px;
  height: 13px;
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap .search-keyword .filter-panel .panel-body .close-filter span {
    width: 4vw;
    height: 4vw;
  }
}
#main #search .cont-wrap .search-keyword .filter-panel .panel-body .close-filter span:hover {
  cursor: pointer;
}
#main #search .cont-wrap .search-keyword .filter-panel .panel-body .close-filter span:before,
#main #search .cont-wrap .search-keyword .filter-panel .panel-body .close-filter span:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 16px;
  background: #333;
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap .search-keyword .filter-panel .panel-body .close-filter span:before,
  #main #search .cont-wrap .search-keyword .filter-panel .panel-body .close-filter span:after {
    height: 4vw;
  }
}
#main #search .cont-wrap .search-keyword .filter-panel .panel-body .close-filter span:before {
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
#main #search .cont-wrap .search-keyword .filter-panel .panel-body .close-filter span:after {
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}
#main #search .cont-wrap .search-keyword .filter-panel .panel-body .close-filter span:hover:before,
#main #search .cont-wrap .search-keyword .filter-panel .panel-body .close-filter span:hover:after {
  background: #427fae;
}
#main #search .cont-wrap button {
  position: relative;
  width: 52px;
  height: 50px;
  background-color: #427fae;
  border: 1px solid #427fae;
  border-left: none;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap button {
    width: 12vw;
    height: 12vw;
  }
}
#main #search .cont-wrap button img {
  width: 30px;
  height: auto;
}
@media screen and (max-width: 767px) {
  #main #search .cont-wrap button img {
    width: 6.5vw;
  }
}
#main .search-category {
  width: 100%;
  padding: 20px 2rem 30px;
  background-color: #ecf2f7;
}
@media screen and (max-width: 767px) {
  #main .search-category {
    padding: 7vw 4vw;
  }
}
#main .search-category .ttl {
  width: 100%;
  margin: 0 0 1.2rem;
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main .search-category .ttl {
    margin: 0 0 4vw;
    font-size: 4.5vw;
  }
}
#main .search-category .list-category {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 980px;
  max-width: 100%;
  margin: 20px auto;
  gap: 20px 20px;
  max-height: 100px;
  overflow: hidden;
  -webkit-transition: max-height 0.4s ease;
  transition: max-height 0.4s ease;
}
@media screen and (max-width: 767px) {
  #main .search-category .list-category {
    width: 100%;
    max-height: 24vw;
    margin: 4vw auto 4vw;
    gap: 2vw 2vw;
  }
}
#main .search-category .list-category.expanded {
  max-height: 2000px;
}
#main .search-category .list-category li {
  width: 70px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
@media screen and (max-width: 767px) {
  #main .search-category .list-category li {
    width: 16vw;
  }
}
#main .search-category .list-category li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}
#main .search-category .list-category li a figure {
  width: 70px;
  height: 70px;
  border: 1px solid #e9eaeb;
}
@media screen and (max-width: 767px) {
  #main .search-category .list-category li a figure {
    width: 16vw;
    height: 16vw;
  }
}
#main .search-category .list-category li a figure img {
  max-width: 100%;
  max-height: 100%;
}
#main .search-category .list-category li a p {
  margin: 5px 0 0;
  color: #333;
  font-size: 10px;
  font-weight: 600;
  line-height: 1.3;
  word-break: break-word;
  text-align: center;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
@media screen and (max-width: 767px) {
  #main .search-category .list-category li a p {
    font-size: 2.7vw;
  }
}
#main .search-category .btn.more-category {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 200px;
  height: 40px;
  margin: 20px auto;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #main .search-category .btn.more-category {
    width: 60vw;
    height: 11vw;
  }
}
#main .search-category .btn.more-category:hover {
  opacity: 1;
  background-color: #f5f5f5;
}
#main .search-category .btn.more-category span {
  font-size: 0.8rem;
  font-weight: 600;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main .search-category .btn.more-category span {
    font-size: 3.5vw;
  }
}
#main #sch_info {
  width: 100%;
  margin: 0;
  padding: 0;
  background-color: #e5e5e5;
}
#main #sch_info p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: 7px;
  background-color: #d3d1d0;
}
#main #sch_info p img {
  width: 30px;
  height: auto;
  margin: 0 10px;
}
#main #sch_info p span {
  color: #427fae;
  font-weight: bold;
}
#main #sch_info .sch_list {
  padding: 1rem;
  background-color: #eee;
}
#main #sch_info .sch_list ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  max-width: 1280px;
  margin: 0 auto;
}
@media screen and (max-width: 767px) {
  #main #sch_info .sch_list ul {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}
#main #sch_info .sch_list ul li {
  width: 50%;
  padding: 10px 20px;
  text-indent: -1rem;
  font-size: 0.9rem;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main #sch_info .sch_list ul li {
    width: 100%;
    padding: 10px 5px 10px 20px;
    font-size: 0.8rem;
  }
}
#main #sch_info .sch_list ul li a {
  /*text-decoration: underline*/
}
#main #sch_info .sch_list ul li a:hover {
  color: #427fae;
  opacity: 1;
}
#main #sch_info .sch_list ul li:before {
  content: '・';
  margin: 0 0.5rem 0 0;
}

#main .list-item {
  padding: 1rem 1.5rem;
  position: relative;
}
@media screen and (max-width: 767px) {
  #main .list-item {
    padding: 7vw 4vw;
  }
}
#main .list-item h2 {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  width: 1280px;
  max-width: 100%;
  margin: 3.5rem auto 2.5rem;
}
@media screen and (max-width: 767px) {
  #main .list-item h2 {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    margin: 1.5rem 0 2rem;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }
}
#main .list-item h2 .ttl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-flex: 1;
  -ms-flex: 1 auto;
  flex: 1 auto;
  max-width: 100%;
  margin: 0 auto;
  font-size: 1.8rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main .list-item h2 .ttl {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 100%;
    padding: 0;
    font-size: 5.8vw;
  }
}
#main .list-item h2 .btn.more {
  position: absolute;
  top: calc(50% - 14px);
  right: 1rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: auto;
  height: 32px;
  margin: 0 5px 0 auto;
  padding: 0 1.8rem;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid #ccc;
  border-radius: 50px;
  cursor: pointer;
  z-index: 2;
}
@media screen and (max-width: 767px) {
  #main .list-item h2 .btn.more {
    width: auto;
    height: 7vw;
    padding: 0 4vw;
    font-size: 3vw;
  }
}
#main .list-item h2 .btn.more:hover {
  background-color: #f5f5f5;
}
#main .list-item .item-list {
  margin: 0;
  padding: 0;
  text-align: center;
}
#main .list-item .item-list.no-item {
  padding: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #fff;
}
#main .list-item .item-list.no-item p.no-item-msg {
  text-align: center;
  font-size: 22px;
  font-weight: 700;
  color: #000;
}
#main .list-item .item-list ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: 100%;
  padding: 0;
  gap: 40px 15px;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul {
    gap: 6vw 4vw;
  }
}
#main .list-item .item-list ul li {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: calc((100% - 60px) / 5);
  height: auto;
  margin: 0;
}
@media screen and (max-width: 1080px) {
  #main .list-item .item-list ul li {
    width: calc((100% - 30px) / 3);
  }
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li {
    width: calc((100% - 4vw) / 2);
  }
}
#main .list-item .item-list ul li.closed a {
  position: relative;
}
#main .list-item .item-list ul li.closed a figure:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  aspect-ratio: 1;
  padding: 0;
  background-image: url(../img/common/layer_closed.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main .list-item .item-list ul li.soldout a {
  position: relative;
}
#main .list-item .item-list ul li.soldout a figure:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  aspect-ratio: 1;
  padding: 0;
  background-image: url(../img/common/layer_soldout.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main .list-item .item-list ul li.preauc a {
  position: relative;
}
#main .list-item .item-list ul li.preauc a figure:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  aspect-ratio: 1;
  padding: 0;
  background-image: url(../img/common/layer_preauc.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main .list-item .item-list ul li.extended a {
  position: relative;
}
#main .list-item .item-list ul li.extended a figure:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  aspect-ratio: 1;
  padding: 0;
  background-image: url(../img/common/layer_extended.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main .list-item .item-list ul li a {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  height: 100%;
  line-height: 1.2;
  cursor: pointer;
}
#main .list-item .item-list ul li a.pct-wrap {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}
#main .list-item .item-list ul li a:not(.pct-wrap) {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
#main .list-item .item-list ul li a figure {
  position: relative;
  aspect-ratio: 1;
  border: 1px solid #e9eaeb;
}
#main .list-item .item-list ul li a figure img {
  height: 100%;
  -o-object-fit: contain;
  object-fit: contain;
}
#main .list-item .item-list ul li a figure .tab-f {
  position: absolute;
  top: 3px;
  left: 3px;
}
#main .list-item .item-list ul li a figure .tab-f span {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 20px;
  padding: 0 14px;
  font-size: 0.7rem;
  font-weight: 600;
  color: #fff;
  line-height: 1;
  background-color: #333;
  border-radius: 2px;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a figure .tab-f span {
    height: 5vw;
    font-size: 2.4vw;
  }
}
#main .list-item .item-list ul li a figure .tab-f span.title-a {
  color: #fff;
  background-color: #333;
}
#main .list-item .item-list ul li a .panel-disc {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  gap: 0.5rem;
  padding: 0.5rem 0;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .panel-disc {
    gap: 2vw;
    padding: 2vw 0;
  }
}
#main .list-item .item-list ul li a .panel-disc .item-name {
  width: 100%;
  margin: 0 0 5px;
  padding: 0;
  line-height: 1.2;
  text-align: left;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .panel-disc .item-name {
    border-bottom: none;
  }
}
#main .list-item .item-list ul li a .panel-disc .item-name .name-title {
  display: inline-block;
  margin: 0 0 0.2rem;
  color: #333;
  font-size: 0.8rem;
  font-weight: 500;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .panel-disc .item-name .name-title {
    margin: 0 0 1vw;
    font-size: 2.8vw;
  }
}
#main .list-item .item-list ul li a .panel-disc .item-name .tab-item {
  display: inline-block;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin: 0;
  padding: 0 0.7rem;
  font-size: 0.65rem;
  font-weight: 600;
  line-height: 1rem;
  border: 1px solid #707070;
  border-radius: 2px;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .panel-disc .item-name .tab-item {
    margin: 0;
    font-size: 2.3vw;
    line-height: 3.2vw;
  }
}
#main .list-item .item-list ul li a .panel-disc .tab-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 3px;
  width: 100%;
  margin: 0;
  padding: 0;
}
#main .list-item .item-list ul li a .panel-disc .tab-wrap li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: auto;
  margin: 0;
  padding: 1px 8px;
  font-size: 0.6rem;
  font-weight: 500;
  white-space: nowrap;
  border: 1px solid #333;
  border-radius: 2px;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .panel-disc .tab-wrap li {
    font-size: 2vw;
  }
}
#main .list-item .item-list ul li a .panel-disc .tab-wrap li.tab-main {
  color: #fff;
  background-color: #333;
}
#main .list-item .item-list ul li a .panel-disc .tab-wrap li.tab-sub {
  border: 1px solid #333;
}
#main .list-item .item-list ul li a .panel-disc .tab-wrap li.tab-standard {
  color: #333;
  border: 1px solid #333;
}
#main .list-item .item-list ul li a .panel-disc .tab-wrap li.top {
  color: #fff;
  font-weight: 600;
  background-color: #e98181;
  border: 1px solid #e98181;
}
#main .list-item .item-list ul li a .panel-disc .tab-wrap li.min-bid {
  color: #e98181;
  font-weight: 600;
  border: 1px solid #e98181;
}
#main .list-item .item-list ul li a .panel-disc .current-price {
  margin: 0;
  padding: 0;
}
#main .list-item .item-list ul li a .panel-disc .current-price .price-c {
  display: inline-block;
  margin-right: 5px;
  font-size: 0.8rem;
  -webkit-transform: translateY(-1px);
  transform: translateY(-1px);
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .panel-disc .current-price .price-c {
    font-size: 2.5vw;
  }
}
#main .list-item .item-list ul li a .panel-disc .current-price .price-v {
  color: #e50a09;
  font-size: 1.2rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .panel-disc .current-price .price-v {
    margin: 0;
    font-size: 4.8vw;
  }
}
#main .list-item .item-list ul li a .panel-disc .current-price .price-u {
  display: inline-block;
  margin-left: 0.1rem;
  color: #e50a09;
  font-size: 0.8rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .panel-disc .current-price .price-u {
    margin-left: 0.5vw;
    font-size: 3vw;
  }
}
#main .list-item .item-list ul li a .panel-disc .current-price .tax-u {
  display: inline-block;
  margin-left: 5px;
  font-size: 0.65rem;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .panel-disc .current-price .tax-u {
    font-size: 2.5vw;
  }
}
#main .list-item .item-list ul li a .pre-bid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 0;
  width: 100%;
  margin: auto auto 0 0;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .pre-bid {
    width: 100%;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }
}
#main .list-item .item-list ul li a .pre-bid li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  padding: 3px 0 3px 24px;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .pre-bid li {
    padding: 0.5vw 0 0.5vw 6vw;
  }
}
#main .list-item .item-list ul li a .pre-bid li.bid-v {
  width: 100%;
  margin: 0;
  background: url('../img/common/icn_hammer_list.png') no-repeat;
  background-size: 14px auto;
  background-position: 1px calc(50% + 1px);
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .pre-bid li.bid-v {
    width: 15vw;
    background-size: 3.5vw auto;
    background-position: 0 center;
  }
}
#main .list-item .item-list ul li a .pre-bid li.bid-v p {
  width: 100%;
  padding: 0;
  font-size: 0.9rem;
  font-weight: 600;
  line-height: 1.1;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .pre-bid li.bid-v p {
    font-size: 3vw;
  }
}
#main .list-item .item-list ul li a .pre-bid li.end-label {
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 100%;
  margin: 0;
  padding: 0;
  font-size: 0.8rem;
  font-weight: 400;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .pre-bid li.end-label {
    font-size: 2.5vw;
  }
}
#main .list-item .item-list ul li a .pre-bid li.end-v {
  width: auto;
  margin: 0;
  background: url('../img/common/icn_clock_list.png') no-repeat;
  background-size: 14px auto;
  background-position: left calc(50% + 1px);
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .pre-bid li.end-v {
    width: 100%;
    background-size: 3vw auto;
    background-position: 0 center;
  }
}
#main .list-item .item-list ul li a .pre-bid li.end-v p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  padding: 0;
}
#main .list-item .item-list ul li a .pre-bid li.end-v p span {
  display: inline-block;
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .pre-bid li.end-v p span {
    font-size: 3vw;
  }
}
#main .list-item .item-list ul li a .pre-bid li.end-v p span.date {
  font-weight: 600;
}
#main .list-item .item-list ul li a .pre-bid li.end-v p span.red {
  color: #e50a09;
}
#main .list-item .item-list ul li a .pre-bid li.end-v p span.end {
  fon-weight: 400;
}
#main .list-item .item-list ul li a .pre-bid li.end-v p span.time {
  display: inline-block;
  margin: 0 0 0 0.5rem;
  font-weight: 600;
}
#main .list-item .item-list ul li a .pre-bid li.favo {
  width: 70px;
  padding: 0 0 0 1.4rem;
  background: url('../img/common/icn_favorite.svg') no-repeat;
  background-size: 16px auto;
  background-position: 0 50%;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .pre-bid li.favo {
    width: calc(100% - 15vw);
    padding: 0 0 0 6vw;
    background-size: 3.5vw auto;
    background-position: 0 center;
  }
}
#main .list-item .item-list ul li a .pre-bid li.favo p {
  font-weight: 700;
  line-height: 1.1;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a .pre-bid li.favo p {
    width: 100%;
    padding: 0;
  }
}
#main .list-item .item-list ul li a .pre-bid li.favo p span {
  display: inline-block;
  font-weight: 700;
}
#main .list-item .item-list ul li a .pre-bid li.favo p span.red {
  color: #e50a09;
}
#main .list-item .item-list ul li a dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  width: calc(100% - 80px);
  min-height: 38px;
  margin: auto auto 0 0;
  padding: 0.5rem 0.2rem 0.5rem;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a dl {
    width: calc(100% - 16.5vw);
  }
}
#main .list-item .item-list ul li a dl dt,
#main .list-item .item-list ul li a dl dd {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0;
  font-size: 0.8rem;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a dl dt,
  #main .list-item .item-list ul li a dl dd {
    font-size: 3.4vw;
  }
}
#main .list-item .item-list ul li a dl dt {
  width: 50px;
  padding: 0 0 0 1.5rem;
  background: url('../img/common/icn_hammer_list.png') no-repeat;
  background-size: 16px auto;
  background-position: left 50%;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a dl dt {
    width: 12vw;
    padding: 0 0 0 5vw;
    background-size: 3.5vw auto;
    background-position: 0 center;
  }
}
#main .list-item .item-list ul li a dl dt .bid-v {
  font-weight: 700;
  line-height: 1.1;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a dl dt .bid-v {
    width: 100%;
    padding: 0;
  }
}
#main .list-item .item-list ul li a dl dd {
  width: calc(100% - 50px);
  padding: 0 0 0 2rem;
  background: url('../img/common/icn_clock_list.png') no-repeat;
  background-size: 16px auto;
  background-position: 10px 50%;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a dl dd {
    width: 14vw;
    padding: 0 0 0 6vw;
    background-size: 3.5vw auto;
    background-position: 0 center;
  }
}
#main .list-item .item-list ul li a dl dd .end-v {
  font-weight: 700;
  line-height: 1.1;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li a dl dd .end-v {
    width: 100%;
    padding: 0;
    font-size: 3vw;
  }
}
#main .list-item .item-list ul li a dl dd .end-v span {
  display: inline-block;
  font-weight: 700;
}
#main .list-item .item-list ul li .btn-foreground-wrap {
  position: absolute;
  bottom: 5px;
  right: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  gap: 5px;
  width: 100px;
  height: 34px;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li .btn-foreground-wrap {
    bottom: 0;
    gap: 1vw;
    width: 20vw;
    height: 9vw;
  }
}
#main .list-item .item-list ul li .btn-foreground-wrap .refresh {
  width: 34px;
  height: 34px;
  padding: 2px 2px;
  background-color: #fff;
  border: 1px solid #e9eaeb;
  border-radius: 50%;
  background-image: url(../img/common/icn_refresh_r.svg);
  background-size: 16px auto;
  background-repeat: no-repeat;
  background-position: 50% 47%;
  cursor: pointer;
  z-index: 1;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li .btn-foreground-wrap .refresh {
    width: 9vw;
    height: 9vw;
    background-size: 4vw auto;
  }
}
#main .list-item .item-list ul li .btn-foreground-wrap .refresh:hover {
  opacity: 1;
  background-color: #fef6f6;
}
#main .list-item .item-list ul li .btn-foreground-wrap .favorite {
  width: 34px;
  height: 34px;
  padding: 2px 2px;
  background-color: #fff;
  border: 1px solid #e9eaeb;
  border-radius: 50%;
  background-image: url(../img/common/icn_favorite.svg);
  background-size: 16px auto;
  background-repeat: no-repeat;
  background-position: 50% 56%;
  cursor: pointer;
  z-index: 1;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list ul li .btn-foreground-wrap .favorite {
    width: 9vw;
    height: 9vw;
    background-size: 4.5vw auto;
  }
}
#main .list-item .item-list ul li .btn-foreground-wrap .favorite:hover {
  opacity: 1;
  background-image: url(../img/common/icn_favorite_blue.svg);
}
#main .list-item .item-list ul li .btn-foreground-wrap .favorite.active {
  background-image: url(../img/common/icn_favorite_blue.svg);
}
#main .list-item .item-list .wrap-btn {
  width: 100%;
  margin: 3rem 0;
  text-align: center;
}
#main .list-item .item-list .wrap-btn .list-more {
  width: 300px;
  height: 56px;
  margin: 0 auto;
  padding: 0.5rem 2rem;
  color: #fff;
  font-size: 1rem;
  font-weight: 700;
  background-color: #427fae;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main .list-item .item-list .wrap-btn .list-more {
    height: 60px;
  }
}

#main #list-new {
  padding: 60px 1.5rem 120px;
}
@media screen and (max-width: 767px) {
  #main #list-new {
    padding: 10vw 0 16vw;
  }
}
#main #list-recommend {
  padding: 40px 1.5rem 100px;
  background-color: #ecf2f7;
}
@media screen and (max-width: 1080px) {
  #main #list-recommend {
    padding: 2rem calc(1.5rem - 15px) 3.5rem 1.5rem;
  }
}
@media screen and (max-width: 767px) {
  #main #list-recommend {
    padding: 10vw 0 10vw 4vw;
  }
}
@media screen and (max-width: 767px) {
  #main #list-recommend h2 {
    padding: 0 4vw 0 0;
  }
}
#main #list-recommend .container {
  padding: 0;
}
#main #list-recommend .container .item-list ul.list-item-gallery {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}
#main #list-recommend .container .item-list ul.list-item-gallery .slick-track > li {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  width: calc((100% - 75px) / 5);
  margin: 0 15px 15px 0;
}
@media screen and (max-width: 1080px) {
  #main #list-recommend .container .item-list ul.list-item-gallery .slick-track > li {
    width: calc((100% - 30px) / 3);
  }
}
@media screen and (max-width: 767px) {
  #main #list-recommend .container .item-list ul.list-item-gallery .slick-track > li {
    width: calc((100% - 15px) / 2);
    margin: 0 4vw 4vw 0;
  }
}

#info {
  padding: 100px 2rem;
}
@media screen and (max-width: 767px) {
  #info {
    padding: 7vw 4vw 10vw;
  }
}
#info .container-grid {
  display: -ms-grid;
  display: grid;
  gap: 10px 40px;
  -ms-grid-columns: 200px 40px auto;
  grid-template-columns: 200px auto;
  -ms-grid-rows: auto 10px auto;
  grid-template-rows: auto auto;
  width: 1180px;
  max-width: 100%;
  margin: 0 auto;
}
#info .container-grid > *:nth-child(1) {
  -ms-grid-row: 1;
  -ms-grid-column: 1;
}
#info .container-grid > *:nth-child(2) {
  -ms-grid-row: 1;
  -ms-grid-column: 3;
}
#info .container-grid > *:nth-child(3) {
  -ms-grid-row: 3;
  -ms-grid-column: 1;
}
#info .container-grid > *:nth-child(4) {
  -ms-grid-row: 3;
  -ms-grid-column: 3;
}
@media screen and (max-width: 767px) {
  #info .container-grid {
    display: block;
    width: 100%;
  }
}
#info .container-grid h2 {
  -ms-grid-column: 1;
  grid-column: 1;
  -ms-grid-row: 1;
  grid-row: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 0;
  padding: 0;
  font-size: 3rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #info .container-grid h2 {
    min-height: 50px;
    padding: 4vw 2vw;
    font-size: 8vw;
  }
}
#info .container-grid h2 span {
  display: block;
  font-size: 1rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #info .container-grid h2 span {
    font-size: 3.5vw;
  }
}
#info .container-grid .more-btn-wrap {
  -ms-grid-column: 1;
  grid-column: 1;
  -ms-grid-row: 2;
  grid-row: 2;
}
@media screen and (max-width: 767px) {
  #info .container-grid .more-btn-wrap {
    padding: 7vw 0;
  }
}
#info .container-grid .more-btn-wrap .btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  height: 40px;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid #ccc;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #info .container-grid .more-btn-wrap .btn {
    width: 60vw;
    height: 11vw;
    margin: 0 auto;
    font-size: 3.5vw;
  }
}
#info .container-grid .more-btn-wrap .btn:hover {
  background-color: #f5f5f5;
}
#info .container-grid .info-item {
  -ms-grid-column: 2;
  grid-column: 2;
  -ms-grid-row: 1;
  -ms-grid-row-span: 2;
  grid-row: 1 / span 2;
  padding: 1rem;
}
@media screen and (max-width: 767px) {
  #info .container-grid .info-item {
    -ms-grid-column: 1;
    grid-column: 1;
    -ms-grid-row: 2;
    grid-row: 2;
    padding: 0;
  }
}
#info .container-grid .info-item li {
  padding: 0;
  border-bottom: 1px solid #ccc;
}
#info .container-grid .info-item li:first-child {
  border-top: 1px solid #ccc;
}
#info .container-grid .info-item li a {
  display: block;
  width: 100%;
  height: 100%;
  padding: 1rem 1.5rem;
  font-weight: 400;
  position: relative;
  letter-spacing: 0;
}
@media screen and (max-width: 767px) {
  #info .container-grid .info-item li a {
    padding: 4vw 2vw;
    font-size: 3.5vw;
    line-height: 1.3;
  }
}
#info .container-grid .info-item li a:hover {
  background-color: #f5f5f5;
}
#info .container-grid .info-item li a span {
  display: inline-block;
  margin-right: 1.5rem;
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #info .container-grid .info-item li a span {
    display: block;
    margin: 0 0 2vw;
    font-size: 3.2vw;
  }
}

#live_contents {
  margin: 0;
}
#live_contents .wrap {
  width: 100%;
  position: relative;
  padding-top: 56.25%;
}
#live_contents .wrap iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

#about {
  width: 100%;
  padding: 160px 4rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: auto;
  background: url('../img/home/<USER>') center top/cover no-repeat;
}
@media screen and (max-width: 767px) {
  #about {
    width: 100%;
    height: auto;
    padding: 18vw 11vw;
    background: url('../img/home/<USER>') center no-repeat;
    background-size: cover;
  }
}
#about .cont-wrap {
  width: 1280px;
  max-width: 100%;
  margin: 0 auto;
}
#about .cont-wrap h2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: baseline;
  -ms-flex-pack: baseline;
  justify-content: baseline;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  margin: 0;
}
#about .cont-wrap h2 .ttl {
  margin: 0 0 0.5rem;
  font-size: 2rem;
  font-weight: 600;
}
#about .cont-wrap h2 .s-ttl {
  margin: 0 0 1rem;
  font-size: 1.2rem;
  font-weight: 600;
}
#about .cont-wrap .read p {
  font-weight: 600;
}
#about .cont-wrap .read p.sb {
  margin: 0.5rem 0 0;
}
#about .cont-wrap .btn-wrap {
  width: 100%;
  margin: 3rem 0;
}
@media screen and (max-width: 767px) {
  #about .cont-wrap .btn-wrap {
    margin: 10vw 0;
  }
}
#about .cont-wrap .btn-wrap a.btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 280px;
  height: 60px;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #about .cont-wrap .btn-wrap a.btn {
    width: 100%;
    height: 15vw;
    margin: 4vw auto;
    font-size: 3.9vw;
  }
}

#signup {
  width: 100%;
  padding: 80px 2rem 100px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: auto;
}
@media screen and (max-width: 767px) {
  #signup {
    width: 100%;
    height: auto;
    padding: 6vw 4vw 8vw;
  }
}
#signup .cont-wrap {
  width: 1280px;
  max-width: 100%;
  margin: 0 auto;
  padding: 70px 2rem 60px;
  background-color: #ecf2f7;
}
@media screen and (max-width: 767px) {
  #signup .cont-wrap {
    padding: 14vw 7vw;
  }
}
#signup .cont-wrap h2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  height: auto;
  margin: 0;
}
#signup .cont-wrap h2:after {
  content: none;
}
#signup .cont-wrap h2 .s-ttl {
  position: relative;
  display: block;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  margin: 0 auto;
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #signup .cont-wrap h2 .s-ttl {
    margin: 0 auto 2vw;
  }
}
#signup .cont-wrap h2 .s-ttl:before {
  content: '';
  position: absolute;
  top: 6px;
  left: -32px;
  width: 1px;
  height: 100%;
  margin: 0;
  background-color: #333;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transform-origin: top left;
  transform-origin: top left;
}
#signup .cont-wrap h2 .s-ttl:after {
  content: '';
  position: absolute;
  top: 5px;
  right: -30px;
  width: 1px;
  height: 100%;
  margin: 0;
  background-color: #000;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-transform-origin: top left;
  transform-origin: top left;
}
#signup .cont-wrap h2 .ttl {
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
  text-align: center;
  line-height: 1.3;
}
@media screen and (max-width: 767px) {
  #signup .cont-wrap h2 .ttl {
    font-size: 7vw;
  }
}
#signup .cont-wrap .read {
  width: 700px;
  max-width: 100%;
  margin: 0 auto;
  padding: 2rem;
}
@media screen and (max-width: 767px) {
  #signup .cont-wrap .read {
    width: 100%;
    padding: 4vw 0;
  }
}
#signup .cont-wrap .read p {
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #signup .cont-wrap .read p {
    font-size: 3.5vw;
  }
}
#signup .cont-wrap .read p.sb {
  margin: 1rem 0 0;
  font-size: 1rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #signup .cont-wrap .read p.sb {
    font-size: 3.9vw;
  }
}
#signup .cont-wrap .read p a {
  color: #427fae;
}
#signup .cont-wrap .read p a:hover {
  text-decoration: underline;
}
#signup .cont-wrap .btn-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: 40px;
  width: 100%;
  margin: 2rem auto;
}
@media screen and (max-width: 767px) {
  #signup .cont-wrap .btn-wrap {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    gap: 6vw;
    margin: 4vw 0;
  }
}
#signup .cont-wrap .btn-wrap a.btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 280px;
  height: 60px;
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
  background-color: #427fae;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #signup .cont-wrap .btn-wrap a.btn {
    width: 100%;
    height: 15vw;
    margin: 0 auto;
    font-size: 3.9vw;
  }
}
#signup .cont-wrap .btn-wrap a.btn.more {
  color: #427fae;
  background-color: #fff;
  border: 1px solid #427fae;
}
/*# sourceMappingURL=home.css.map */
