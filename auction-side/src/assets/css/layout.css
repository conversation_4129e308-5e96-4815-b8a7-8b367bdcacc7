@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *wrap
 * ***********************************************************************/
.container {
  width: 1280px;
  max-width: 100%;
  margin: 0 auto;
  padding: 1rem;
}
@media screen and (max-width: 767px) {
  .container {
    width: 100%;
    max-width: 100%;
    padding: 4vw;
  }
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *header
 * ***********************************************************************/
header {
  border-bottom: 1px solid #e9eaeb;
  background-color: #fff;
  position: relative;
}
@media screen and (max-width: 767px) {
  header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    width: 100%;
    height: 14vw;
  }
}
header .wrap-header-elm {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm {
    width: 100%;
  }
}
header .wrap-header-elm .l-header-info-links {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  height: 34px;
  padding: 0;
  background-color: #e9eaeb;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .l-header-info-links {
    display: none;
  }
}
header .wrap-header-elm .l-header-info-links .l-header-info-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: calc(1280px + 2.2rem);
  max-width: 100%;
  margin: 0 auto;
  padding: 0 1.1rem;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .l-header-info-links .l-header-info-item {
    padding: 0 2rem;
  }
}
header .wrap-header-elm .l-header-info-links .l-header-info-item li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
header .wrap-header-elm .l-header-info-links .l-header-info-item li.language-swich .lang-wrap {
  position: relative;
  margin: 0;
  width: auto;
}
header
  .wrap-header-elm
  .l-header-info-links
  .l-header-info-item
  li.language-swich
  .lang-wrap:after {
  content: '';
  display: block;
  width: 4px;
  height: 4px;
  border-top: 2px solid #9e9e9e;
  border-left: 2px solid #9e9e9e;
  -webkit-transform: rotate(-135deg);
  transform: rotate(-135deg);
  position: absolute;
  right: 4px;
  top: calc(50% - 3px);
}
header
  .wrap-header-elm
  .l-header-info-links
  .l-header-info-item
  li.language-swich
  .lang-wrap
  select {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  height: 20px;
  padding: 0 16px 2px 22px;
  font-size: 0.8em;
  font-weight: bold;
  text-decoration: underline;
  align-items: center;
  background: url('../img/common/icn_global.svg') no-repeat 0 50%;
  background-size: 16px 16px;
  border: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
}
header
  .wrap-header-elm
  .l-header-info-links
  .l-header-info-item
  li.language-swich
  .lang-wrap
  select
  option {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  padding: 0;
}
header .wrap-header-elm .main-nav-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: calc(1280px + 2rem);
  max-width: 100%;
  height: 76px;
  margin: 0 auto;
  padding: 0 1rem;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap {
    height: auto;
    padding: 0.5rem 1.5rem;
  }
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap {
    width: 100%;
    max-width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
  }
}
header .wrap-header-elm .main-nav-wrap .h-top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex: 0;
  -ms-flex: 0 1 200px;
  flex: 0 1 200px;
  height: 65px;
  padding: 0;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .h-top {
    height: auto;
  }
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap .h-top {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    width: 100%;
    min-height: 35px;
    padding: 0;
    height: 100%;
    -webkit-box-flex: 1;
    -ms-flex: auto;
    flex: auto;
  }
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-logo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 0;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap .h-top .h-top-logo {
    position: absolute;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    margin: 0;
    padding: 0;
  }
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-logo a.logo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 200px;
  max-width: 38vw;
  -webkit-transition: none;
  transition: none;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap .h-top .h-top-logo a.logo {
    width: 28vw;
    max-width: 200px;
  }
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-logo a.logo img {
  width: 100%;
  height: auto;
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-logo p {
  color: #fff;
  font-size: 16px;
  position: relative;
  line-height: 1;
  margin-left: 22px;
  padding-left: 20px;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap .h-top .h-top-logo p {
    font-size: 2.5vw;
    margin-left: 11px;
    padding-left: 10px;
  }
  header .wrap-header-elm .main-nav-wrap .h-top .h-top-logo p::before {
    width: 1px;
    height: 15px;
  }
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-logo p::before {
  content: '';
  display: block;
  width: 2px;
  height: 28px;
  background-color: #fff;
  position: absolute8;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 3vw;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  width: 100px;
  margin: 0 3.8vw 0 0;
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu .account button,
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu .account a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 4vw;
  height: auto;
  min-height: 5vw;
  padding: 0;
  background-color: transparent;
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu .account button img,
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu .account a img {
  width: 100%;
  height: auto;
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu .lang-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  margin: 0;
  width: auto;
  height: auto;
  min-height: 5vw;
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu .lang-wrap select {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  height: 6vw;
  padding: 0 0 0 4vw;
  font-size: 3vw;
  font-weight: bold;
  text-decoration: underline;
  align-items: center;
  background: url('../img/common/icn_global.svg') no-repeat 0 calc(50% + 0.1vw);
  background-size: 3.6vw 3.6vw;
  border: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu .lang-wrap select option {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  padding: 0;
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu ul {
    gap: 3vw;
  }
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu ul li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border: 0.5px solid #fff;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu ul li {
    width: auto;
    height: 100%;
    margin: 0;
  }
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu ul li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #fff;
  font-weight: 700;
  font-size: 1rem;
  background-repeat: no-repeat;
  background-position: left center;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu ul li a {
    font-size: 3.5vw;
  }
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu ul li a.btn-favorite img {
  width: 24px;
  height: auto;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu ul li a.btn-favorite img {
    width: 5vw;
  }
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu ul li a.btn-member img {
  width: 24px;
  height: auto;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu ul li a.btn-member img {
    width: 4.5vw;
  }
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu a.btn-bid {
  background-size: 18px auto;
  padding-left: 23px;
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu a.btn-fav {
  background-size: 15px auto;
  padding-left: 20px;
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu a.btn-logout {
  background-size: 15px auto;
  padding-left: 18px;
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu a.btn-page img {
  width: 16px;
}
header .wrap-header-elm .main-nav-wrap .h-top .h-top-menu a.btn-lang {
  background-size: 15px auto;
  padding-left: 20px;
}
header .wrap-header-elm .main-nav-wrap .nav-elm {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: auto;
  margin: 0 1.5rem;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    margin: 0 0.5rem;
    padding: 0.5rem 0;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-category {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  width: 110px;
  height: 100%;
  margin: 0 1.2rem 0 0;
  color: #333;
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-category li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: 50px;
  padding: 0;
  list-style-type: none;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-category li {
    height: 30px;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-category li a.nav-label {
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  color: #333;
  font-size: 0.8rem;
  font-weight: 600;
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-category li a.nav-label:after {
  content: '';
  position: absolute;
  right: 2px;
  top: 50%;
  width: 4px;
  height: 4px;
  border-right: 2px solid #bcbcbc;
  border-bottom: 2px solid #bcbcbc;
  -webkit-transform: translateY(-50%) rotate(45deg);
  transform: translateY(-50%) rotate(45deg);
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-category li a.nav-label:hover {
  color: #427fae;
  opacity: 1;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  li
  a.nav-label:hover:after {
  border-color: #427fae;
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-category:hover .menu-list {
  display: block;
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-category .menu-list {
  display: none;
  position: absolute;
  top: 50px;
  left: -180px;
  width: 800px;
  margin: 0 auto;
  padding: 0;
  z-index: 20;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-category .menu-list {
    top: 30px;
    left: -180px;
    width: 650px;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-category .menu-list .arrow-box {
  width: 100%;
  height: 16px;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap {
  width: 100%;
  padding: 20px 10px;
  background-color: #f5f5f5;
  border-radius: 10px;
  -webkit-box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.35);
  box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.35);
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap
  .category-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  padding: 0.5rem 1rem;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap
  .category-box
  .category-all {
  width: 100%;
  padding: 2px 5px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap
  .category-box
  .category-all
  p {
  font-size: 0.8rem;
  font-weight: 500;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap
  .category-box
  .category-all
  p
  a {
  display: inline-block;
  padding: 1px 16px;
  color: #333;
  font-size: 0.8rem;
  font-weight: 500;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  white-space: nowrap;
  cursor: pointer;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap
  .category-box
  .category-all
  p
  a:hover {
  opacity: 1;
  background-color: #e8e8e8;
  border-radius: 50px;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap
  .category-box
  .category-top {
  width: 150px;
  padding: 2px 5px;
  border-right: 1px dotted #ccc;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap
  .category-box
  .category-top
  p {
  font-size: 0.8rem;
  font-weight: 500;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap
  .category-box
  .category-top
  p
  a {
  display: inline-block;
  padding: 1px 16px;
  color: #333;
  font-size: 0.8rem;
  font-weight: 500;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  white-space: nowrap;
  cursor: pointer;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap
  .category-box
  .category-top
  p
  a:hover {
  opacity: 1;
  background-color: #e8e8e8;
  border-radius: 50px;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap
  .category-box
  .category-secondary {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap
  .category-box
  .category-secondary
  ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  padding-left: 10px;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap
  .category-box
  .category-secondary
  ul:before {
  content: '';
  position: absolute;
  top: 0;
  left: 220px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 16px solid #f5f5f5;
}
@media screen and (max-width: 1080px) {
  header
    .wrap-header-elm
    .main-nav-wrap
    .nav-elm
    .search-elm
    .search-category
    .menu-list
    .panel-wrap
    .category-box
    .category-secondary
    ul:before {
    left: 230px;
  }
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap
  .category-box
  .category-secondary
  ul
  li {
  position: relative;
  width: auto;
  height: auto;
  padding: 1px 2px;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap
  .category-box
  .category-secondary
  ul
  li
  a {
  display: inline-block;
  padding: 1px 16px;
  color: #333;
  font-size: 0.8rem;
  font-weight: 500;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  white-space: nowrap;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-category
  .menu-list
  .panel-wrap
  .category-box
  .category-secondary
  ul
  li
  a:hover {
  opacity: 1;
  background-color: #e8e8e8;
  border-radius: 50px;
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  width: 108px;
  height: 100%;
  margin: 0 0 0 1.2rem;
  color: #333;
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: 50px;
  padding: 0;
  list-style-type: none;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu li {
    height: 30px;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu li a.nav-label {
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  color: #333;
  font-size: 0.8rem;
  font-weight: 600;
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu li a.nav-label:after {
  content: '';
  position: absolute;
  right: 4px;
  top: calc(50% - 0px);
  width: 4px;
  height: 4px;
  border-right: 2px solid #bcbcbc;
  border-bottom: 2px solid #bcbcbc;
  -webkit-transform: translateY(-50%) rotate(45deg);
  transform: translateY(-50%) rotate(45deg);
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu li a.nav-label:hover {
  color: #427fae;
  opacity: 1;
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu li a.nav-label:hover:after {
  border-color: #427fae;
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu:hover .menu-list {
  display: block;
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu .menu-list {
  display: none;
  position: absolute;
  top: 50px;
  z-index: 20;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu .menu-list {
    top: 26px;
    left: -100px;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu .menu-list .arrow-box {
  width: 100%;
  height: 16px;
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu .menu-list ul {
  width: 220px;
  padding: 10px 0;
  background-color: #f5f5f5;
  border-radius: 10px;
  -webkit-box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.35);
  box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.35);
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu .menu-list ul:before {
  content: '';
  position: absolute;
  top: 0;
  left: 30px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 16px solid #f5f5f5;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu .menu-list ul:before {
    left: 134px;
  }
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu .menu-list ul:before {
    left: 80px;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu .menu-list ul li {
  position: relative;
  width: 220px;
  height: 40px;
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu .menu-list ul li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  padding: 20px 20px;
  color: #333;
  font-size: 0.8rem;
  font-weight: 500;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .info-menu .menu-list ul li a:hover {
  opacity: 1;
  background-color: #e8e8e8;
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-keyword {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 260px;
  height: 44px;
  border: 1px solid #e9eaeb;
  border-radius: 60px;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-keyword {
    width: 190px;
    height: 30px;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-keyword input {
  width: calc(100% - 44px);
  padding: 0 0 0 16px;
  font-size: 0.8rem;
  line-height: 1;
  background-color: transparent;
  border: none;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-keyword input {
    width: calc(100% - 34px);
    padding: 0 0 0 12px;
  }
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-keyword input {
    width: 170px;
  }
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-keyword
  input::-webkit-input-placeholder {
  color: #ccc;
  font-size: 0.8rem;
  font-weight: 500;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-keyword
  input::-moz-placeholder {
  color: #ccc;
  font-size: 0.8rem;
  font-weight: 500;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-keyword
  input:-ms-input-placeholder {
  color: #ccc;
  font-size: 0.8rem;
  font-weight: 500;
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-keyword
  input::-ms-input-placeholder {
  color: #ccc;
  font-size: 0.8rem;
  font-weight: 500;
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-keyword input::placeholder {
  color: #ccc;
  font-size: 0.8rem;
  font-weight: 500;
}
@media screen and (max-width: 1080px) {
  header
    .wrap-header-elm
    .main-nav-wrap
    .nav-elm
    .search-elm
    .search-keyword
    input::-webkit-input-placeholder {
    font-size: 0.7rem;
    -webkit-transform: translateY(-1px);
    transform: translateY(-1px);
  }
  header
    .wrap-header-elm
    .main-nav-wrap
    .nav-elm
    .search-elm
    .search-keyword
    input::-moz-placeholder {
    font-size: 0.7rem;
    transform: translateY(-1px);
  }
  header
    .wrap-header-elm
    .main-nav-wrap
    .nav-elm
    .search-elm
    .search-keyword
    input:-ms-input-placeholder {
    font-size: 0.7rem;
    transform: translateY(-1px);
  }
  header
    .wrap-header-elm
    .main-nav-wrap
    .nav-elm
    .search-elm
    .search-keyword
    input::-ms-input-placeholder {
    font-size: 0.7rem;
    transform: translateY(-1px);
  }
  header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-keyword input::placeholder {
    font-size: 0.7rem;
    -webkit-transform: translateY(-1px);
    transform: translateY(-1px);
  }
}
header
  .wrap-header-elm
  .main-nav-wrap
  .nav-elm
  .search-elm
  .search-keyword
  input.side-search-keyword {
  height: 100%;
  line-height: 42px;
  padding-top: 0;
  padding-bottom: 0;
}
@media screen and (max-width: 1080px) {
  header
    .wrap-header-elm
    .main-nav-wrap
    .nav-elm
    .search-elm
    .search-keyword
    input.side-search-keyword {
    height: 30px;
    line-height: 30px;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-keyword button {
  position: relative;
  width: 44px;
  height: 44px;
  padding: 0;
  background-color: transparent;
  border-top-right-radius: 50px;
  border-bottom-right-radius: 50px;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-keyword button {
    width: 34px;
    height: 30px;
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
  }
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-keyword button {
    width: 40px;
    border-left: none;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-keyword button img {
  width: 24px;
  height: auto;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .search-elm .search-keyword button img {
    width: 20px;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    gap: 20px;
    margin: 0;
    padding: 0.7rem 0.5rem;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn .nav-mypage {
  width: 56px;
  height: 50px;
  color: #333;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn .nav-mypage {
    width: auto;
    height: auto;
    margin: 0;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn .nav-mypage a {
  text-align: center;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn .nav-mypage a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn .nav-mypage a:hover {
    color: #427fae;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn .nav-mypage a img {
  width: 20px;
  height: auto;
  margin: 0 auto;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn .nav-mypage a img {
    width: 14px;
    margin-right: 4px;
    -webkit-transform: translateY(1px);
    transform: translateY(1px);
  }
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn .nav-mypage a img {
    width: 24px;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn .nav-mypage a img.bid {
  width: 18px;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn .nav-mypage a img.bid {
    width: 12px;
    margin-right: 5px;
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn .nav-mypage a img.bidded {
  width: 18px;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn .nav-mypage a img.bidded {
    width: 13px;
    margin-right: 5px;
    -webkit-transform: translateY(1px);
    transform: translateY(1px);
  }
}
header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn .nav-mypage a span {
  width: 100%;
  display: block;
  color: #333;
  font-size: 10px;
  font-weight: 600;
  text-align: center;
  line-height: 1;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn .nav-mypage a span {
    font-size: 0.8rem;
    font-weight: 600;
  }
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .main-nav-wrap .nav-elm .nav-btn .nav-mypage a span {
    display: none;
  }
}

/* ヘッダー
 * *========================================== */
/* ---------------------------
 * *SPハンバーガーメニュー
 * *----------------------------- */
header .h-top p.btnMenu {
  position: relative;
  width: 14vw;
  height: 14vw;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 14vw;
  flex: 0 0 14vw;
}
header .h-top p.btnMenu span.ham::before {
  content: '';
  display: block;
  background-color: #333;
  width: 3.5vw;
  height: 0.45vw;
  border-radius: 0.45vw;
  position: absolute;
  top: 6.8vw;
  left: 1.5vw;
  -webkit-transform: translateX(3.2vw);
  transform: translateX(3.2vw);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
header .h-top p.btnMenu::before,
header .h-top p.btnMenu::after {
  content: '';
  display: block;
  background-color: #333;
  width: 5vw;
  height: 0.4vw;
  border-radius: 0.4vw;
  position: absolute;
  right: 50%;
  -webkit-transform: translateX(50%);
  transform: translateX(50%);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
header .h-top p.btnMenu::before {
  top: 5.4vw;
}
header .h-top p.btnMenu::after {
  bottom: 5.4vw;
}
header .h-top p.btnMenu.close::before {
  top: 50%;
  -webkit-transform: translate(50%, -50%) rotate(45deg);
  transform: translate(50%, -50%) rotate(45deg);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
header .h-top p.btnMenu.close::after {
  top: 50%;
  bottom: auto;
  -webkit-transform: translate(50%, -50%) rotate(-45deg);
  transform: translate(50%, -50%) rotate(-45deg);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
header .h-top p.btnMenu.close span.ham::before {
  display: none;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

/* グロナビ
 * *========================================== */
/* ---------------------------
 * *PC
 * *-----------------------------
 *
 * header .gNav
 *  background-color: #fff
 *  border-bottom: 1px solid #d9d9d9
 *  display: block !important
 *
 *  nav
 *    a
 *      font-size: 14px
 *      font-weight: 700
 *
 *    > ul
 *      display: flex
 *
 *      > li
 *        border-left: 1px solid #d9d9d9
 *        width: calc(100% / 9)
 *        height: 50px
 *        position: relative
 *
 *        &:last-of-type
 *          border-right: 1px solid #d9d9d9
 *
 *        &::after
 *          content: ""
 *          display: block
 *          width: 100%
 *          height: 2px
 *          position: absolute
 *          bottom: -1px
 *          left: 0
 *          right: 0
 *          background-color: transparent
 *          transition: all 0.08s linear
 *
 *        &:hover::after
 *          background-color: #01a7ac
 *
 *        > a
 *          display: flex
 *          align-items: center
 *          justify-content: center
 *          width: 100%
 *          height: 100%
 *          padding: 5px 10px
 *
 *        &.nav-black > a
 *          background-color: #333F48
 *          color: #fff
 *
 *        > a:hover
 *          opacity: 1
 *
 *        width: calc((100% - 80px - 130px) / 8)
 *
 *        &.nav-top
 *          width: 80px
 *
 *        &.nav-first
 *          width: 130px
 *
 *        > ul
 *          display: none
 *          position: absolute
 *          top: 51px
 *          left: 0
 *          background-color: rgba(255, 255, 255, 0.9)
 *          width: 100%
 *          padding: 15px 0
 *          z-index: 1
 *
 *        &.nav-access > ul
 *          width: 190px
 *
 *          /* 親メニュ幅より大きく
 *
 *        &.nav-entry > ul
 *          width: 125px
 *
 *          /* 親メニュ幅より大きく
 *
 *        &.nav-overview > ul
 *          width: 140px
 *
 *          /* 親メニュ幅より大きく
 *
 *        &:hover > ul
 *          display: block
 *          animation-name: fade-basic
 *          animation-duration: .5s
 *
 *        > ul li a
 *          display: block
 *          padding: 7px 15px
 *          color: #000 */
/* ---------------------------
 * *SP　ヘッダーナビ
 * *----------------------------- */
@media screen and (max-width: 767px) {
  header .gNav {
    display: none;
    position: absolute;
    top: 14vw;
    left: 0;
    right: 0;
    width: 100%;
    height: 100vh;
    background-color: #fbfbfb;
    z-index: 100;
  }
  header .gNav nav a {
    font-size: 16px;
  }
  header .gNav nav > ul > li {
    border-bottom: 1px solid #e7e7e7;
  }
  header .gNav nav > ul > li > a {
    min-height: 50px;
    padding: 5px 40px 5px 30px;
    position: relative;
  }
  header .gNav nav > ul > li > p {
    font-size: 16px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-weight: 700;
    min-height: 50px;
    padding: 4vw 10vw 4vw 4vw;
    position: relative;
  }
  header .gNav nav > ul > li.account {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    gap: 3vw;
    padding: 3vw 4vw;
  }
  header .gNav nav > ul > li.account .btn {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 50%;
    padding: 3.5vw 4vw;
    color: #fff;
    font-size: 3.5vw;
    font-weight: 500;
    line-height: 1.1;
    border-radius: 4px;
  }
  header .gNav nav > ul > li.account .btn.entry {
    background-color: #e98181;
  }
  header .gNav nav > ul > li.account .btn.login {
    background-color: #427fae;
  }
  header .gNav nav > ul > li.account .btn.mypage {
    background-color: #e98181;
  }
  header .gNav nav > ul > li.account .btn.logout {
    color: #427fae;
    background-color: #fff;
    border: 1px solid #427fae;
  }
  header .gNav nav > ul > li.search {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0.8rem 1rem;
  }
  header .gNav nav > ul > li.search input {
    height: 12vw;
    width: calc(100% - 12vw);
    padding: 2vw 2vw 2vw 3vw;
    border: 1px solid #e9eaeb;
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  header .gNav nav > ul > li.search button {
    width: 12vw;
    height: 12vw;
    background-color: #427fae;
    border: 1px solid #427fae;
    border-left: none;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
  header .gNav nav > ul > li.search button img {
    width: 6.5vw;
    height: auto;
  }
  header .gNav nav > ul > li.nav-black > a,
  header .gNav nav > ul > li.nav-black > p {
    background-color: #eef2f5;
    color: #333;
    font-size: 3.8vw;
  }
  header .gNav nav > ul > li > a::after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
  }
  header .gNav nav > ul > li > p::before,
  header .gNav nav > ul > li > p::after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
  }
  header .gNav nav > ul > li:not(.nav-black) > ul li a::after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
  }
  header .gNav nav > ul > li:not(.nav-black) > ul li a::after {
    width: 8px;
    height: 8px;
    border-top: #000 2px solid;
    border-right: #000 2px solid;
    -webkit-transform: rotate(45deg) translateY(-50%);
    transform: rotate(45deg) translateY(-50%);
    right: 28px;
  }
  header .gNav nav > ul > li > a::after {
    width: 8px;
    height: 8px;
    border-top: #000 2px solid;
    border-right: #000 2px solid;
    -webkit-transform: rotate(45deg) translateY(-50%);
    transform: rotate(45deg) translateY(-50%);
    right: 28px;
  }
  header .gNav nav > ul > li.nav-black > a::after {
    border-top-color: #ccc;
    border-right-color: #ccc;
  }
  header .gNav nav > ul > li.nav-black > p::before,
  header .gNav nav > ul > li.nav-black > p::after {
    background-color: #666;
  }
  header .gNav nav > ul > li.nav-black > ul li {
    border-top: none;
    display: block;
    width: 100%;
  }
  header .gNav nav > ul > li > p::before {
    width: 16px;
    height: 2px;
    background-color: #000;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 20px;
  }
}
@media screen and (max-width: 767px) and (max-width: 767px) {
  header .gNav nav > ul > li > p::before {
    width: 3.5vw;
    right: 4vw;
  }
}
@media screen and (max-width: 767px) {
  header .gNav nav > ul > li > p::after {
    width: 2px;
    height: 16px;
    background-color: #000;
    -webkit-transform: translateY(-50%) rotate(0deg);
    transform: translateY(-50%) rotate(0deg);
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    right: 27px;
  }
}
@media screen and (max-width: 767px) and (max-width: 767px) {
  header .gNav nav > ul > li > p::after {
    height: 3.5vw;
    right: 5.6vw;
  }
}
@media screen and (max-width: 767px) {
  header .gNav nav > ul > li > p.close::before {
    display: none;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
  }
  header .gNav nav > ul > li > p.close::after {
    -webkit-transform: translateY(-50%) rotate(90deg);
    transform: translateY(-50%) rotate(90deg);
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
  }
  header .gNav nav > ul > li > ul {
    display: none;
    padding: 0;
    border-top: 1px solid #e9eaeb;
    background-color: #fff;
  }
  header .gNav nav > ul > li > ul li + li {
    border-top: 1px solid #e9eaeb;
  }
  header .gNav nav > ul > li > ul li:first-child > a {
    border-top: none;
  }
  header .gNav nav > ul > li > ul li a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 100%;
    color: #797979;
    font-size: 3.6vw;
    font-weight: 500;
    min-height: 40px;
    padding: 3.5vw 8vw;
    position: relative;
    border-top: 1px solid #efefef;
  }
  header .gNav nav > ul > li > ul li a:after {
    content: '';
    width: 6px;
    height: 6px;
    border: 0;
    border-top: solid 2px #797979;
    border-right: solid 2px #797979;
    position: absolute;
    top: calc(50% + 1px);
    right: 20px;
    margin-top: -4px;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
  }
}
@media screen and (max-width: 767px) and (max-width: 767px) {
  header .gNav nav > ul > li > ul li a:after {
    width: 1.2vw;
    height: 1.2vw;
    right: 5.3vw;
  }
}
@media screen and (max-width: 767px) {
  header .gNav nav > ul > li > p.ttl {
    padding: 0;
  }
  header .gNav nav > ul > li > p.ttl:before,
  header .gNav nav > ul > li > p.ttl:after {
    display: none;
  }
  header .gNav nav > ul > li > p.ttl a {
    display: block;
    width: 100%;
    height: 100%;
    padding: 5px 1rem;
    color: #fff;
    font-weight: 700;
  }
  header .gNav nav > ul > li:not(.nav-black) > ul li a {
    padding-left: calc(30px + 1em);
  }
  header .gNav nav .line-logo {
    width: 100%;
    padding: 60px 0;
    background-color: #fff;
  }
}
@media screen and (max-width: 767px) and (max-width: 767px) {
  header .gNav nav .line-logo {
    padding: 10vw;
    border-top: 1px solid #efefef;
  }
}
@media screen and (max-width: 767px) {
  header .gNav nav .line-logo .cont-wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 1180px;
    max-width: 100%;
    margin: 0 auto;
    padding: 0 2rem;
  }
}
@media screen and (max-width: 767px) and (max-width: 767px) {
  header .gNav nav .line-logo .cont-wrap {
    width: 100%;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}
@media screen and (max-width: 767px) {
  header .gNav nav .line-logo .cont-wrap .pct {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 160px;
    height: 100%;
  }
}
@media screen and (max-width: 767px) and (max-width: 767px) {
  header .gNav nav .line-logo .cont-wrap .pct {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 60%;
    margin: 0 auto 5vw;
  }
}
@media screen and (max-width: 767px) {
  header .gNav nav .line-logo .cont-wrap .pct a:hover {
    opacity: 0.8;
  }
  header .gNav nav .line-logo .cont-wrap .pct img {
    width: 100%;
    height: auto;
  }
  header .gNav nav .line-logo .cont-wrap .sns ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    gap: 8px;
  }
  header .gNav nav .line-logo .cont-wrap .sns ul li {
    width: 40px;
    height: 40px;
    padding: 0;
  }
  header .gNav nav .line-logo .cont-wrap .sns ul li a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 100%;
    height: 100%;
  }
  header .gNav nav .line-logo .cont-wrap .sns ul li a img {
    width: 30px;
    height: auto;
  }
  header .gNav nav .line-logo .cont-wrap .sns ul li a img.facebook {
    width: 26px;
  }
  header .gNav nav .line-logo .cont-wrap .sns ul li a img.x {
    width: 22px;
  }
  header .gNav nav .line-logo .cont-wrap .sns ul li a img.instagram {
    width: 24px;
  }
  header .gNav nav .line-copyright {
    max-width: 100%;
    margin: 0 auto;
    width: 100%;
    padding: 24px 1rem;
    background-color: #fff;
    border-top: 1px solid #e9eaeb;
  }
  header .gNav nav .line-copyright .cont-wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 1180px;
    max-width: 100%;
    margin: 0 auto;
    padding: 0 2rem;
  }
}
@media screen and (max-width: 767px) and (max-width: 767px) {
  header .gNav nav .line-copyright .cont-wrap {
    width: 100%;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}
@media screen and (max-width: 767px) {
  header .gNav nav .line-copyright .cont-wrap ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
}
@media screen and (max-width: 767px) and (max-width: 767px) {
  header .gNav nav .line-copyright .cont-wrap ul {
    width: 100%;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding: 2vw 0 6vw;
  }
}
@media screen and (max-width: 767px) {
  header .gNav nav .line-copyright .cont-wrap ul li {
    width: auto;
    padding: 0 1.5rem 0 0;
  }
}
@media screen and (max-width: 767px) and (max-width: 767px) {
  header .gNav nav .line-copyright .cont-wrap ul li {
    padding: 2vw 4vw;
  }
}
@media screen and (max-width: 767px) {
  header .gNav nav .line-copyright .cont-wrap ul li a {
    padding: 0;
    font-size: 13px;
  }
}
@media screen and (max-width: 767px) and (max-width: 767px) {
  header .gNav nav .line-copyright .cont-wrap ul li a {
    font-size: 3vw;
  }
}
@media screen and (max-width: 767px) {
  header .gNav nav .line-copyright .cont-wrap ul li a:hover {
    text-decoration: underline;
  }
  header .gNav nav .line-copyright .cont-wrap .copyright small {
    padding: 0;
    font-size: 10px;
    font-weight: 400;
  }
}
@media screen and (max-width: 767px) and (max-width: 767px) {
  header .gNav nav .line-copyright .cont-wrap .copyright small {
    font-size: 2vw;
  }
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *main
 * *********************************************************************** */
#main {
  display: block;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main {
    padding: 0 0 12vw;
  }
}
#main.stock,
#main.auction {
  padding-bottom: 60px;
}
@media screen and (max-width: 767px) {
  #main.stock,
  #main.auction {
    padding-bottom: 40px;
  }
}
#main #pNav {
  padding: 0 1rem;
  background-color: #fff;
  border-bottom: 1px solid #e9eaeb;
  overflow: hidden;
}
@media screen and (max-width: 1080px) {
  #main #pNav {
    padding: 0 1.5rem;
  }
}
#main #pNav ul {
  width: 100%;
  max-width: 1280px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  padding: 10px 0;
  margin: 0 auto;
  overflow-x: auto;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #main #pNav ul {
    width: 100%;
  }
}
#main #pNav ul li {
  font-size: 0.75rem;
}
@media screen and (max-width: 767px) {
  #main #pNav ul li {
    font-size: 2vw;
  }
}
#main #pNav ul li:not(:last-of-type)::after {
  content: '/';
  display: inline-block;
  margin: 0 0.8rem;
}
#main #pNav ul li a {
  color: #427fae;
}
#main #pNav ul li a:hover {
  text-decoration: underline;
}

/* パンくずリスト
 * *==========================================
 *
 * #main #pNav > ul
 *  @include sp
 *    overflow-x: auto
 *    word-break: keep-all
 *    white-space: wrap
 *    -webkit-overflow-scrolling: touch */
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *footer
 * *********************************************************************** */
footer {
  position: relative;
  background-color: #e8e8e8;
  color: #fff;
  padding: 0;
}
@media screen and (max-width: 767px) {
  footer {
    padding: 0;
    background-color: #fff;
  }
}
footer #page_top {
  position: fixed;
  display: block;
  width: 42px;
  height: 42px;
  right: 1rem;
  bottom: 1rem;
  background-color: #427fae;
  border-radius: 50%;
  z-index: 10;
}
@media screen and (max-width: 767px) {
  footer #page_top {
    width: 12vw;
    height: 12vw;
  }
}
footer #page_top a {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  cursor: pointer;
  z-index: 11;
}
footer #page_top a:hover {
  opacity: 0.7;
}
footer #page_top a:before {
  content: '';
  width: 6px;
  height: 6px;
  border: 0;
  border-top: solid 2px #fff;
  border-right: solid 2px #fff;
  position: absolute;
  top: calc(50% + 1px);
  left: calc(50% - 4px);
  margin-top: -4px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
footer nav {
  width: 1180px;
  max-width: 100%;
  margin: 0 auto;
  padding: 60px 2rem 80px;
}
@media screen and (max-width: 1080px) {
  footer nav {
    padding: 60px 2rem 80px;
  }
}
@media screen and (max-width: 767px) {
  footer nav {
    margin: 0;
    padding: 0;
  }
}
footer nav ul li a {
  display: inline-block;
  color: #333;
  font-size: 14px;
}
@media screen and (max-width: 767px) {
  footer nav ul li a {
    position: relative;
    padding: 5px 1rem;
  }
}
footer nav ul li a[target='_blank']::after {
  content: '';
  display: inline-block;
  background: url('../img/common/ic_link_blank_gray.svg') center/cover no-repeat;
  width: 12px;
  height: 12px;
  position: relative;
  top: 1px;
  margin-left: 6px;
}
@media screen and (max-width: 767px) {
  footer nav ul li a[target='_blank']::after {
    content: none;
    background: none;
  }
}
footer nav .fNav_pc {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: 60px;
}
@media screen and (max-width: 1080px) {
  footer nav .fNav_pc {
    gap: 4vw;
  }
}
footer nav .fNav_pc .fNav-1 {
  width: 77%;
  padding: 0 20px 0 0;
}
@media screen and (max-width: 1080px) {
  footer nav .fNav_pc .fNav-1 {
    width: 70%;
  }
}
footer nav .fNav_pc .fNav-1 ul.list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
footer nav .fNav_pc .fNav-1 ul.list li p {
  margin: 0 0 0.5rem;
  color: #333;
  font-size: 0.9rem;
  font-weight: 700;
}
footer nav .fNav_pc .fNav-1 ul.list li ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}
footer nav .fNav_pc .fNav-1 ul.list li ul li a {
  font-size: 0.8rem;
}
footer nav .fNav_pc .fNav-2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  width: 23%;
  padding: 0 0 0 40px;
  border-left: 1px solid #ccc;
}
@media screen and (max-width: 1080px) {
  footer nav .fNav_pc .fNav-2 {
    width: 20%;
    padding: 0 0 0 4vw;
  }
}
footer nav .fNav_pc .fNav-2 ul {
  margin: 0 auto 0 0;
}
footer nav .fNav_pc .fNav-2 ul li {
  padding: 0 0 0.4rem;
}
footer nav .fNav_pc .fNav-2 ul li p a {
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
}
footer nav .fNav_sp {
  display: none;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  padding: 0;
}
footer nav .fNav_sp ul li.bg-gray {
  background-color: #eef2f5;
}
footer nav .fNav_sp ul li p,
footer nav .fNav_sp ul li a {
  color: #333;
}
footer nav .fNav_sp > ul > li:first-child {
  border-top: 1px solid #e7e7e7;
}
footer nav .fNav_sp > ul > li ul a::after {
  border-top-color: #666;
  border-right-color: #666;
}
footer nav .fNav_sp > ul > li a {
  min-height: 48px;
  padding: 4vw 10vw 4vw 4vw;
  font-size: 3.8vw;
  font-weight: 700;
  border-bottom: 1px solid #e7e7e7;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
}
footer nav .fNav_sp > ul > li p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  min-height: 48px;
  padding: 4vw 10vw 4vw 4vw;
  font-size: 3.8vw;
  font-weight: 700;
  border-bottom: 1px solid #e7e7e7;
}
footer nav .fNav_sp > ul > li p::before,
footer nav .fNav_sp > ul > li p::after {
  content: '';
  display: block;
  position: absolute;
  top: 50%;
}
footer nav .fNav_sp > ul > li p::before {
  width: 16px;
  height: 2px;
  background-color: #666;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 20px;
}
@media screen and (max-width: 767px) {
  footer nav .fNav_sp > ul > li p::before {
    width: 3.5vw;
    right: 4vw;
  }
}
footer nav .fNav_sp > ul > li p::after {
  width: 2px;
  height: 16px;
  background-color: #666;
  -webkit-transform: translateY(-50%) rotate(0deg);
  transform: translateY(-50%) rotate(0deg);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  right: 27px;
}
@media screen and (max-width: 767px) {
  footer nav .fNav_sp > ul > li p::after {
    height: 3.5vw;
    right: 5.6vw;
  }
}
footer nav .fNav_sp > ul > li p.close::before {
  display: none;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
footer nav .fNav_sp > ul > li p.close::after {
  -webkit-transform: translateY(-50%) rotate(90deg);
  transform: translateY(-50%) rotate(90deg);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
footer nav .fNav_sp > ul > li p ul li a::after {
  width: 8px;
  height: 8px;
  border-top: #666 2px solid;
  border-right: #666 2px solid;
  -webkit-transform: rotate(45deg) translateY(-50%);
  transform: rotate(45deg) translateY(-50%);
  right: 22px;
}
footer nav .fNav_sp > ul > li ul,
footer nav .fNav_sp > ul > li dl {
  display: none;
  background-color: #fff;
}
footer nav .fNav_sp > ul > li ul a {
  color: #333;
  padding-left: 42px;
}
footer nav .fNav_sp > ul > li dl a {
  color: #01a7ac;
  padding-left: 42px;
}
footer nav .fNav_sp > ul > li dl a::after {
  display: none;
  padding-right: 0;
}
footer nav .fNav_sp > ul > li ul a {
  min-height: 48px;
  font-size: 3.6vw;
  font-weight: 500;
  border-bottom: 1px solid #e7e7e7;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
footer nav .fNav_sp > ul > li ul a:after {
  content: '';
  width: 6px;
  height: 6px;
  border: 0;
  border-top: solid 2px #797979;
  border-right: solid 2px #797979;
  position: absolute;
  top: calc(50% + 1px);
  right: 5.7vw;
  margin-top: -4px;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
footer nav .fNav_sp > ul > li ul a[target='_blank'] span:after {
  content: '';
  display: inline-block;
  background: url('../img/common/ic_link_blank_gray.svg') no-repeat;
  width: 3.5vw;
  height: 3.5vw;
  position: absolute;
  top: calc(50% - 1.7vw);
  margin-left: 1vw;
}
footer .line-logo {
  width: 100%;
  padding: 60px 0;
  background-color: #fff;
}
@media screen and (max-width: 767px) {
  footer .line-logo {
    padding: 10vw;
  }
}
footer .line-logo .cont-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 1180px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 2rem;
}
@media screen and (max-width: 767px) {
  footer .line-logo .cont-wrap {
    width: 100%;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}
footer .line-logo .cont-wrap .pct {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 160px;
  height: 100%;
}
@media screen and (max-width: 767px) {
  footer .line-logo .cont-wrap .pct {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 64%;
    margin: 0 auto 5vw;
  }
}
footer .line-logo .cont-wrap .pct a:hover {
  opacity: 0.8;
}
footer .line-logo .cont-wrap .pct img {
  width: 100%;
  height: auto;
}
footer .line-logo .cont-wrap .sns ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 8px;
}
footer .line-logo .cont-wrap .sns ul li {
  width: 40px;
  height: 40px;
  padding: 0;
}
footer .line-logo .cont-wrap .sns ul li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
footer .line-logo .cont-wrap .sns ul li a img {
  width: 30px;
  height: auto;
}
footer .line-logo .cont-wrap .sns ul li a img.facebook {
  width: 26px;
}
footer .line-logo .cont-wrap .sns ul li a img.x {
  width: 22px;
}
footer .line-logo .cont-wrap .sns ul li a img.instagram {
  width: 24px;
}
footer .line-copyright {
  max-width: 100%;
  margin: 0 auto;
  width: 100%;
  padding: 24px 1rem;
  background-color: #333;
}
footer .line-copyright .cont-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 1180px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 2rem;
}
@media screen and (max-width: 767px) {
  footer .line-copyright .cont-wrap {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    width: 100%;
    padding: 0;
  }
}
footer .line-copyright .cont-wrap ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}
@media screen and (max-width: 767px) {
  footer .line-copyright .cont-wrap ul {
    width: 100%;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding: 2vw 0 6vw;
  }
}
footer .line-copyright .cont-wrap ul li {
  width: auto;
  padding: 0 1.5rem 0 0;
}
@media screen and (max-width: 767px) {
  footer .line-copyright .cont-wrap ul li {
    padding: 2vw 4vw;
  }
}
footer .line-copyright .cont-wrap ul li a {
  color: #fff;
  padding: 0;
  font-size: 13px;
}
@media screen and (max-width: 767px) {
  footer .line-copyright .cont-wrap ul li a {
    font-size: 3vw;
  }
}
footer .line-copyright .cont-wrap ul li a:hover {
  text-decoration: underline;
}
footer .line-copyright .cont-wrap .copyright small {
  padding: 0;
  color: #fff;
  font-size: 10px;
  font-weight: 400;
}
@media screen and (max-width: 767px) {
  footer .line-copyright .cont-wrap .copyright small {
    font-size: 2vw;
  }
}
/*# sourceMappingURL=layout.css.map */
