import {defineStore} from 'pinia'
import {computed, ref, type ComputedRef, type Ref} from 'vue'
import useApi from '../composables/useApi'

interface FunctionOptions {
  satei: 0 | 1
  shuppin: 0 | 1
  htmlMail: 0 | 1
  csvDownload: 0 | 1
  haisouDaiKou: 0 | 1
  stockLinkage: 0 | 1
  memberLinkage: 0 | 1
  shuppinDaiKou: 0 | 1
  productLinkage: 0 | 1
  creditCardPayment: 0 | 1
  topNicknameDisplay: 0 | 1
}

interface BidOptions {
  bid_limit_fuin: number
  bid_limit_seri: number
  extension_fuin: number
  extension_seri: number
  bid_cancel_fuin: number
  bid_cancel_seri: number
  instant_win_fuin: number
  instant_win_seri: number
  sankaseigen_fuin: number
  sankaseigen_seri: number
  minimum_price_fuin: number
  minimum_price_seri: number
  winner_select_fuin: number
  winner_select_seri: number
  bid_success_price_fuin: string | number
  bid_success_price_seri: number
}

interface TenantSettings {
  tenant_no: number
  tenant_id: string
  tenant_name: string
  company_name: string
  contact_email: string
  domain: string
  admin_language_code: string
  function_options: FunctionOptions
  bid_options: BidOptions
  search_result_view_mode: 'panel' | 'row'
  language_code_list: string[]
}

interface TenantSettingsStore {
  tenantSettings: Ref<TenantSettings | null>
  isLoading: Ref<boolean>
  error: Ref<Error | null>
  lastFetched: Ref<Date | null>

  isLoaded: ComputedRef<boolean>
  tenantName: ComputedRef<string>
  companyName: ComputedRef<string>
  functionOptions: ComputedRef<FunctionOptions>
  bidOptions: ComputedRef<BidOptions>
  searchResultViewMode: ComputedRef<'panel' | 'row'>
  supportedLanguages: ComputedRef<string[]>

  // Actions
  fetchTenantSettings: (forceRefresh?: boolean) => Promise<TenantSettings | void>
  clearTenantSettings: () => void
  isFeatureEnabled: (featureName: string) => boolean
  getBidOption: <T = any>(optionName: string, defaultValue?: T) => T
}

export const useTenantSettingsStore = defineStore('tenantSettings', (): TenantSettingsStore => {
  const {apiExecute} = useApi()

  // State
  const tenantSettings: Ref<TenantSettings | null> = ref(null)
  const isLoading: Ref<boolean> = ref(false)
  const error: Ref<Error | null> = ref(null)
  const lastFetched: Ref<Date | null> = ref(null)

  // Getters
  const isLoaded: ComputedRef<boolean> = computed(() => tenantSettings.value !== null)
  const tenantName: ComputedRef<string> = computed(() => tenantSettings.value?.tenant_name || '')
  const companyName: ComputedRef<string> = computed(() => tenantSettings.value?.company_name || '')
  const functionOptions: ComputedRef<FunctionOptions> = computed(() => {
    if (!tenantSettings.value?.function_options) {
      return {
        satei: 0,
        shuppin: 0,
        htmlMail: 0,
        csvDownload: 0,
        haisouDaiKou: 0,
        stockLinkage: 0,
        memberLinkage: 0,
        shuppinDaiKou: 0,
        productLinkage: 0,
        creditCardPayment: 0,
        topNicknameDisplay: 0,
      }
    }
    return tenantSettings.value.function_options
  })
  const bidOptions: ComputedRef<BidOptions> = computed(() => {
    if (!tenantSettings.value?.bid_options) {
      return {
        bid_limit_fuin: 0,
        bid_limit_seri: 0,
        extension_fuin: 0,
        extension_seri: 0,
        bid_cancel_fuin: 0,
        bid_cancel_seri: 0,
        instant_win_fuin: 0,
        instant_win_seri: 0,
        sankaseigen_fuin: 0,
        sankaseigen_seri: 0,
        minimum_price_fuin: 0,
        minimum_price_seri: 0,
        winner_select_fuin: 0,
        winner_select_seri: 0,
        bid_success_price_fuin: 0,
        bid_success_price_seri: 0,
      }
    }
    return tenantSettings.value.bid_options
  })
  const searchResultViewMode: ComputedRef<'panel' | 'row'> = computed(
    () => tenantSettings.value?.search_result_view_mode || 'panel'
  )
  const supportedLanguages: ComputedRef<string[]> = computed(
    () => tenantSettings.value?.language_code_list || ['ja']
  )

  async function fetchTenantSettings(forceRefresh = false): Promise<TenantSettings | void> {
    if (isLoaded.value && !forceRefresh) {
      return tenantSettings.value!
    }

    isLoading.value = true
    error.value = null

    try {
      // Always fetch tenant settings regardless of authentication status
      // Backend will handle determining tenant ID from either auth or domain
      const response = await apiExecute('public/get-tenant-settings', {})
      tenantSettings.value = response as TenantSettings
      lastFetched.value = new Date()
      return response as TenantSettings
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      console.error('Failed to fetch tenant settings:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  function clearTenantSettings(): void {
    tenantSettings.value = null
    error.value = null
    lastFetched.value = null
  }

  function isFeatureEnabled(featureName: string): boolean {
    const options = functionOptions.value as any
    return options[featureName] === 1
  }

  function getBidOption<T = any>(optionName: string, defaultValue?: T): T {
    return (bidOptions.value[optionName] ?? defaultValue) as T
  }

  return {
    // State
    tenantSettings,
    isLoading,
    error,
    lastFetched,

    // Getters
    isLoaded,
    tenantName,
    companyName,
    functionOptions,
    bidOptions,
    searchResultViewMode,
    supportedLanguages,

    // Actions
    fetchTenantSettings,
    clearTenantSettings,
    isFeatureEnabled,
    getBidOption,
  }
})
