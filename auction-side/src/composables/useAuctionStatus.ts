import type {AuctionStatusClass} from '@/components/common/auction/types'
import type {FormattedAuctionItem} from '@/composables/_type'

/**
 * Composable for determining auction status classes
 * @returns Object containing the getAuctionStatusClass function
 */
export default function useAuctionStatus() {
  /**
   * Determines the appropriate CSS class for an auction item based on its status
   * @param item - The auction item to evaluate
   * @returns The status class string
   */
  const getAuctionStatusClass = (
    item: FormattedAuctionItem | null | undefined
  ): AuctionStatusClass => {
    if (!item || !item.bid_status) return ''

    const now = new Date()
    const endDate = new Date(item.bid_status.end_datetime || item.end_datetime)
    const startDate = new Date(item.bid_status.start_datetime || item.start_datetime)

    // SOLD OUT
    if (item.sold_out) {
      return 'soldout'
    }

    // オークション終了
    if (now > endDate) {
      return 'closed'
    }

    // 下見期間中
    if (now < startDate) {
      return 'preauc'
    }

    // 延長中
    if (item.bid_status.extending) {
      return 'extended'
    }

    // Default to new auction (empty class)、label is [New]
    return ''
  }

  return {
    getAuctionStatusClass,
  }
}
