const Define = require(`${process.env.COMMON_LAYER_PATH}define`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool(process.env.PGHOST)
const {DeleteObjectCommand, S3Client} = require('@aws-sdk/client-s3')
const validateLocalized = require('./validateLocalized')

const excelProcess = ({params, tenantNo, adminNo, adminLanguageCode}) => {
  console.log('excelProcess')
  console.log('params = ', params)

  if (!params.urls || Object.values(params.urls).filter(<PERSON><PERSON><PERSON>).length === 0) {
    return Promise.reject({
      status: 400,
      errors: {urls: Define.MESSAGE.E020006},
    })
  }

  return Promise.resolve()
    .then(() => {
      console.log('CHECK EXCEL URL')
      const validateResult = {}

      if (!params.validation_mode) {
        // Check exhibition_no
        if (!Validator.checkRequired(params.exhibition_no)) {
          validateResult.exhibition_no = Define.MESSAGE.E000406
        } else if (!Validator.checkNaturalNumber(params.exhibition_no)) {
          validateResult.exhibition_no = Define.MESSAGE.E000422
        }
      }

      const checkData = {...params}
      // Check file urls
      for (const key of Object.keys(checkData.urls)) {
        if (checkData.urls[key] && !checkData.urls[key].match(/^.*\.(xlsx)$/)) {
          validateResult[`url_${key}`] = Common.format(
            Define.MESSAGE.E020007,
            key
          )
        }
      }

      // Check for tenant language list and file urls
      return Promise.all([
        pool.rlsQuery(
          tenantNo,
          Define.QUERY.GET_TENANT_LANGUAGE_LIST_FUNCTION,
          [tenantNo]
        ),
        pool.rlsQuery(tenantNo, Define.QUERY.GET_CONSTANTS_BY_KEYS, [
          ['LANGUAGE_CODE'],
          tenantNo,
          null,
        ]),
      ])
        .then(([tenantLangList, constants]) => {
          console.log('Tenant language list data: ', tenantLangList)
          console.log('constants: ', constants)
          const languageList =
            tenantLangList && tenantLangList.length > 0
              ? tenantLangList[0].language_code_list
              : []
          for (const lang of languageList) {
            if (!checkData.urls[lang]) {
              const langTxt =
                constants?.find(
                  x => x.key_string === 'LANGUAGE_CODE' && x.value1 === lang
                )?.value2 || lang
              validateResult[`url_${lang}`] = Common.format(
                Define.MESSAGE.E020011,
                [langTxt]
              )
            }
          }
          return Promise.resolve()
        })
        .then(() => {
          if (Object.keys(validateResult).length > 0) {
            return Promise.reject({
              status: 400,
              errors: validateResult,
            })
          }
          console.log('CHECK EXCEL URL SUCCESSFUL')
          return Promise.resolve()
        })
    })
    .then(() => {
      // Get exhibition end_datetime for validation
      // exhibitionEndDateTime = params.end_datetime
      return Promise.resolve()
    })
    .then(async () => {
      console.log('READ XLSX FILEs')
      console.log('S3_BUCKET: ', process.env.S3_BUCKET)
      console.log('urls: ', params.urls)
      // Get valid urls
      const fileUrls = Object.keys(params.urls)
        .filter(x => !!params.urls[x])
        .map(x => {
          return {
            lang: x,
            url: params.urls[x],
          }
        })
      console.log('fileUrls: ', fileUrls)

      // Read files from S3
      return Promise.all(
        fileUrls.map(file => {
          const fileUrl = file.url
          console.log(`Reading file: ${fileUrl}`)
          return Base.parseS3Xlsx(process.env.S3_BUCKET, fileUrl).then(data => {
            console.log('data: ', data)

            // Get field_map by language_code
            if (!data || !data.length || data.length === 0) {
              return Promise.reject({
                status: 400,
                errors: {
                  [`url_${file.lang}`]: Define.MESSAGE.E000166,
                },
              })
            }

            return Promise.resolve()
              .then(() => {
                return pool.rlsQuery(tenantNo, Define.QUERY.GET_ITEM_FIELDS, [
                  tenantNo,
                  file.lang,
                ])
              })
              .then(fields => {
                console.log('fields: ', fields)
                if (!fields || fields.length === 0) {
                  return Promise.reject({
                    status: 400,
                    errors: {
                      [`url_${file.lang}`]: Define.MESSAGE.E000166,
                    },
                  })
                }
                const field = fields[0]

                // Common fields
                const commonFields = {
                  ...Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME,
                }
                // Localized fields
                const localizedFields = field.field_list?.reduce(
                  (acc, field) => {
                    acc[field.physical_name] = field.logical_name
                    return acc
                  },
                  {}
                )
                console.log('commonFields: ', commonFields)
                console.log('localizedFields: ', localizedFields)

                return Base.mapColumnNameToId(data, {
                  ...commonFields,
                  ...localizedFields,
                })
                  .then(xlsxData => {
                    console.log('READ FILE SUCCESSFUL!')
                    console.log('xlsxData: ', xlsxData)
                    return Promise.resolve({...file, field, data: xlsxData})
                  })
                  .catch(error => {
                    if (
                      error &&
                      error.status === 400 &&
                      error.message === Define.MESSAGE.E000166
                    ) {
                      error.message = Define.MESSAGE.E000179
                    }
                    return Promise.reject(error)
                  })
              })
          })
        })
      ).then(results => {
        console.log('READ FILES RESULTS: ', results)
        return Promise.resolve(results)
      })
    })
    .then(importedLots => {
      console.log('CHECK MAXIMUM 500 LINES')
      if (importedLots && importedLots.some(x => x.data?.length > 500)) {
        const err = {
          status: 400,
          message: Define.MESSAGE.E000182,
        }
        return Promise.reject(err)
      }
      return Promise.resolve(importedLots)
    })
    .then(importedLots => {
      // Cross check language files
      // Check common fields is same on all files
      console.log('CHECK CROSS LANGUAGE FILES')
      const commonFields = Object.keys(
        Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME
      )
      // Check value of each common field is same on all files for the same manage_no
      // Create a map to track manage_no and their values
      // Return error with row index if any field value is different
      const manageNoMap = new Map()
      for (const lot of importedLots) {
        for (const row of lot.data) {
          const manageNo = row.manage_no
          if (!manageNo) continue
          if (!manageNoMap.has(manageNo)) {
            manageNoMap.set(manageNo, {
              row,
              langs: {[lot.lang]: row},
            })
          } else {
            const entry = manageNoMap.get(manageNo)
            entry.langs[lot.lang] = row
          }
        }
      }
      for (const [, entry] of manageNoMap.entries()) {
        const langs = Object.keys(entry.langs)
        if (langs.length < importedLots.length) {
          // Not all languages have this manage_no
          continue
        }
        const firstRow = entry.row
        for (const lang of langs) {
          const row = entry.langs[lang]
          for (const field of commonFields) {
            if (row[field] !== firstRow[field]) {
              return Promise.reject({
                status: 400,
                errors: {
                  [`url_${lang}`]: Common.format(Define.MESSAGE.E020009, [
                    field,
                  ]),
                },
              })
            }
          }
        }
      }
      console.log('CHECK COMMON FIELDS SUCCESSFUL!')
      return Promise.resolve(importedLots)
    })
    .then(importedLots => {
      console.log('Validate all files')

      return Promise.all(
        importedLots.map(lot => {
          return validateLocalized({
            pool,
            tenantNo,
            params: {
              ...lot,
              exhibition_no: params.exhibition_no,
            },
          })
            .then(convertedItems => {
              console.log(`VALIDATE ${lot.lang} FILE SUCCESSFUL!`)
              // Return converted data with the lot info
              return Promise.resolve({
                ...lot,
                convertedData: convertedItems,
              })
            })
            .catch(error => {
              console.log(`VALIDATE ${lot.lang} FILE ERROR: `, error)
              return Promise.resolve(error)
            })
        })
      ).then(results => {
        console.log('CHECK ALL FILES RESULTS: ', results)
        const errors = results.filter(res => {
          return (
            typeof res !== 'undefined' &&
            res !== null &&
            Object.keys(res).length > 0 &&
            !res.convertedData // If it has convertedData, it's successful
          )
        })
        console.log(`errors:`, errors?.length)
        if (errors.length > 0) {
          const resErrors = errors.reduce((acc, error) => {
            if (error && Object.keys(error).length > 0) {
              Object.keys(error).forEach(key => {
                acc[key] = error[key]
              })
            }
            return acc
          }, {})
          console.log('resErrors: ', resErrors?.length)
          return Promise.reject({
            status: 400,
            errors: resErrors,
          })
        }
        // Return lots with converted data
        const convertedLots = results
          .filter(res => res.convertedData)
          .map(res => {
            // Convert back to Excel format for subsequent processing
            const convertedExcelData = res.convertedData.map(item => ({
              manage_no: item.manage_no,
              lowest_bid_price: item.lowest_bid_price,
              lowest_bid_accept_price: item.lowest_bid_accept_price,
              quantity: 1,
              lowest_bid_quantity: 1,
              lowest_bid_accept_quantity: 1,
              ...item.free_field, // Spread the rest of the fields
            }))

            return {
              ...res,
              data: convertedExcelData, // Replace original data with converted data
            }
          })
        return Promise.resolve(convertedLots)
      })
    })
    .then(items => {
      console.log('IS VALIDATE MODE: ', params.validation_mode)
      // Validation successful
      if (params.validation_mode === true) {
        const response = {
          status: 200,
          message: '',
        }
        return Promise.reject(response)
      }
      return Promise.resolve(items)
    })
    .then(items => {
      // Prepare items for upsert
      console.log('PREPARE ITEMS FOR UPSERT')
      const prepareItems = []
      const manageNoMap = new Map()
      for (const item of items) {
        for (const row of item.data) {
          const manageNo = row.manage_no
          if (!manageNo) continue
          if (!manageNoMap.has(manageNo)) {
            manageNoMap.set(manageNo, {
              manage_no: manageNo,
              item_no: null,
              lot_no: null,
              quantity: row.quantity,
              lowest_bid_quantity: row.lowest_bid_quantity,
              lowest_bid_accept_quantity: row.lowest_bid_accept_quantity,
              lowest_bid_price: row.lowest_bid_price,
              lowest_bid_accept_price: row.lowest_bid_accept_price,
              localized_json_array: [],
            })
          }
          const entry = manageNoMap.get(manageNo)
          entry.localized_json_array.push({
            language_code: item.lang,
            field_map: row,
          })
        }
      }
      manageNoMap.forEach(entry => {
        prepareItems.push(entry)
      })
      console.log('prepareItems: ', prepareItems.length)
      return Promise.resolve(prepareItems)
    })
    .then(items => {
      if (items.length === 0) {
        console.log('NO ITEMS TO UPSERT')
        return Promise.resolve([])
      }
      console.log('ITEMS TO UPSERT: ', items.length)
      // Update or insert items
      return Common.splitAll(
        item => {
          return Promise.resolve()
            .then(() => {
              // Get original item info if existed
              const sql_params = [
                tenantNo,
                params.exhibition_no,
                item.manage_no,
                adminLanguageCode,
              ]
              return pool
                .rlsQuery(
                  tenantNo,
                  Define.QUERY.GET_ITEM_BY_MANAGE_NO,
                  sql_params
                )
                .then(result => {
                  console.log('result: ', result)
                  if (
                    typeof result === 'undefined' ||
                    result === null ||
                    result.length === 0
                  ) {
                    item.item_no = null
                  } else {
                    item.item_no = result[0].item_no
                    item.lot_no = result[0].lot_no
                  }
                  return Promise.resolve()
                })
                .catch(error => {
                  console.log('error: ', error)
                  item.item_no = null
                  return Promise.resolve()
                })
            })
            .then(() => {
              // Update or insert item
              const sql_params = [item, tenantNo, adminNo]
              return pool
                .rlsQuery(tenantNo, Define.QUERY.UPSERT_ITEM, sql_params)
                .then(result => {
                  console.log('result = ', result)
                  // Get item_no
                  item.item_no = result[0].f_upsert_item
                  return Promise.resolve()
                })
            })
            .then(() => {
              // Upsert lot
              const sql_params = [
                params.exhibition_no,
                tenantNo,
                [item.item_no],
                item.lot_no,
                item.quantity,
                item.lowest_bid_quantity,
                item.lowest_bid_accept_quantity,
                item.lowest_bid_price,
                item.lowest_bid_accept_price,
                null,
                adminNo,
              ]
              return pool
                .rlsQuery(tenantNo, Define.QUERY.UPSERT_LOT, sql_params)
                .then(result => {
                  console.log('result = ', JSON.stringify(result))
                  return Promise.resolve()
                })
            })
        },
        items.map((item, index) => {
          return [item, index]
        }),
        100,
        1
      ).then(results => {
        console.log('results: ', results)
        return Promise.resolve(items)
      })
    })
    .then(result => {
      console.log('DELETE XLSX FILE')
      // Don't delete file when in validation mode
      if (params.validation_mode) {
        return Promise.resolve()
      }
      // Get file url encoded
      const fileUrls = params.urls
      console.log('fileUrls: ', fileUrls)

      const client = new S3Client({region: process.env.AWS_REGION})
      return Promise.all(
        Object.keys(fileUrls).map(lang => {
          const fileUrl = fileUrls[lang]
          console.log(`Deleting file: ${fileUrl}`)
          if (!fileUrl) {
            return Promise.resolve()
          }
          const command = new DeleteObjectCommand({
            Bucket: process.env.S3_BUCKET,
            Key: fileUrl,
          })
          return client.send(command).then(() => Promise.resolve(result))
        })
      )
    })
    .then(result => {
      console.log(JSON.stringify(result))
      return Promise.resolve({status: 200, message: 'Success'})
    })
}

module.exports = excelProcess
